# AAA Inventory System - API Reference

## InventoryManager (Singleton)

The central manager for the entire inventory system.

### Properties
- `item_database: ItemDatabase` - The item database containing all item definitions
- `containers: Dictionary` - Dictionary of all registered inventory containers
- `player_inventory: InventoryContainer` - Default player inventory container
- `is_initialized: bool` - Whether the system has been initialized

### Methods

#### Container Management
```gdscript
register_container(container: InventoryContainer, container_id: String = "") -> String
unregister_container(container_id: String) -> bool
get_container(container_id: String) -> InventoryContainer
get_all_containers() -> Dictionary
```

#### Item Operations
```gdscript
create_item(item_id: String, quantity: int = 1) -> ItemInstance
add_item_to_container(item: ItemInstance, container_id: String) -> bool
add_item_to_player(item: ItemInstance) -> bool
give_item_to_player(item_id: String, quantity: int = 1) -> bool
```

#### Transfer Operations
```gdscript
transfer_item(item: ItemInstance, from_container_id: String, to_container_id: String, from_slot: int = -1, to_slot: int = -1) -> bool
```

#### Query Operations
```gdscript
get_total_item_count(item_id: String) -> int
find_item_instances(item_id: String) -> Array[Dictionary]
consume_item(item_id: String, quantity: int) -> bool
```

#### Save/Load Operations
```gdscript
save_inventory_data(file_path: String = "user://inventory_save.json") -> bool
load_inventory_data(file_path: String = "user://inventory_save.json") -> bool
```

#### Statistics
```gdscript
get_system_stats() -> Dictionary
```

## ItemData (Resource)

Base class for all item definitions.

### Enums
```gdscript
enum ItemType { WEAPON, ARMOR, CONSUMABLE, MATERIAL, TOOL, QUEST, MISC }
enum ItemRarity { COMMON, UNCOMMON, RARE, EPIC, LEGENDARY, MYTHIC }
```

### Properties
```gdscript
@export var id: String
@export var name: String
@export var description: String
@export var icon: Texture2D
@export var type: ItemType
@export var rarity: ItemRarity
@export var max_stack_size: int
@export var value: int
@export var can_drop: bool
@export var can_trade: bool
@export var can_sell: bool
@export var tags: PackedStringArray
@export var custom_properties: Dictionary
```

### Methods
```gdscript
get_rarity_color() -> Color
get_type_name() -> String
get_rarity_name() -> String
has_tag(tag: String) -> bool
is_valid() -> bool
get_tooltip_text() -> String
```

## ItemInstance (RefCounted)

Represents a specific instance of an item with unique properties.

### Properties
```gdscript
var item_data: ItemData
var quantity: int
var durability: float
var unique_id: String
var modifications: Dictionary
var instance_properties: Dictionary
var created_timestamp: float
var modified_timestamp: float
```

### Signals
```gdscript
signal quantity_changed(old_quantity: int, new_quantity: int)
signal durability_changed(old_durability: float, new_durability: float)
```

### Methods
```gdscript
set_quantity(new_quantity: int) -> void
set_durability(new_durability: float) -> void
can_stack_with(other: ItemInstance) -> bool
get_stack_space() -> int
add_quantity(amount: int) -> int
remove_quantity(amount: int) -> int
split(split_quantity: int) -> ItemInstance
duplicate() -> ItemInstance
is_broken() -> bool
is_empty() -> bool
get_total_value() -> int
to_dict() -> Dictionary
```

### Static Methods
```gdscript
static func from_dict(data: Dictionary, item_database) -> ItemInstance
```

## InventoryContainer (Resource)

Manages a collection of items with configurable constraints.

### Enums
```gdscript
enum ContainerType { PLAYER_INVENTORY, STORAGE_CONTAINER, EQUIPMENT_SLOTS, HOTBAR, CRAFTING_GRID, LOOT_CONTAINER, VENDOR_INVENTORY, QUEST_REWARDS }
```

### Properties
```gdscript
@export var container_type: ContainerType
@export var max_slots: int
@export var allowed_types: Array[ItemData.ItemType]
@export var allowed_rarities: Array[ItemData.ItemRarity]
@export var required_tags: PackedStringArray
@export var forbidden_tags: PackedStringArray
@export var allow_auto_sort: bool
@export var allow_stacking: bool
@export var container_name: String
var slots: Array[ItemInstance]
var container_id: String
```

### Signals
```gdscript
signal item_added(item: ItemInstance, slot_index: int)
signal item_removed(item: ItemInstance, slot_index: int)
signal item_moved(item: ItemInstance, from_slot: int, to_slot: int)
signal container_changed()
```

### Methods
```gdscript
resize(new_size: int) -> bool
can_add_item(item: ItemInstance) -> bool
can_add_item_to_slot(item: ItemInstance, slot_index: int) -> bool
add_item(item: ItemInstance) -> bool
add_item_to_slot(item: ItemInstance, slot_index: int) -> bool
remove_item_from_slot(slot_index: int) -> ItemInstance
remove_quantity_from_slot(slot_index: int, quantity: int) -> ItemInstance
move_item(from_slot: int, to_slot: int) -> bool
get_item_at_slot(slot_index: int) -> ItemInstance
get_all_items() -> Array[ItemInstance]
get_empty_slot_count() -> int
is_full() -> bool
is_empty() -> bool
clear() -> Array[ItemInstance]
```

## InventoryOperations (Static Class)

Advanced inventory operations with validation and error handling.

### Enums
```gdscript
enum OperationResult { SUCCESS, FAILED_INVALID_ITEM, FAILED_INVALID_CONTAINER, FAILED_INVALID_SLOT, FAILED_CONTAINER_FULL, FAILED_ITEM_RESTRICTIONS, FAILED_INSUFFICIENT_QUANTITY, FAILED_CANNOT_STACK, FAILED_OPERATION_NOT_ALLOWED }
```

### Static Methods
```gdscript
static func add_item_smart(container: InventoryContainer, item: ItemInstance) -> OperationResult
static func remove_item_quantity(container: InventoryContainer, item_id: String, quantity: int) -> Dictionary
static func move_item_between_containers(from_container: InventoryContainer, to_container: InventoryContainer, from_slot: int, to_slot: int = -1) -> OperationResult
static func split_item_stack(container: InventoryContainer, slot_index: int, split_quantity: int) -> OperationResult
static func merge_item_stacks(container: InventoryContainer, slot1: int, slot2: int) -> OperationResult
static func auto_sort_container(container: InventoryContainer, sort_criteria: String = "type_rarity") -> OperationResult
static func compact_container(container: InventoryContainer) -> OperationResult
static func execute_batch_operation(operations: Array[Dictionary]) -> Dictionary
static func get_operation_result_message(result: OperationResult) -> String
```

## ItemFilter (RefCounted)

Advanced item filtering and categorization system.

### Methods
```gdscript
apply_preset(preset_name: String) -> bool
set_name_filter(filter_text: String)
add_type_filter(type: ItemData.ItemType)
remove_type_filter(type: ItemData.ItemType)
toggle_type_filter(type: ItemData.ItemType)
add_rarity_filter(rarity: ItemData.ItemRarity)
remove_rarity_filter(rarity: ItemData.ItemRarity)
toggle_rarity_filter(rarity: ItemData.ItemRarity)
add_tag_filter(tag: String)
remove_tag_filter(tag: String)
set_value_range(min_value: int = -1, max_value: int = -1)
set_durability_range(min_durability: float = -1, max_durability: float = -1)
add_custom_predicate(predicate: Callable)
remove_custom_predicate(predicate: Callable)
set_sort_criteria(sort_by: String, ascending: bool = true)
clear_filters()
passes_filter(item: ItemInstance) -> bool
filter_items(items: Array[ItemInstance]) -> Array[ItemInstance]
filter_container(container: InventoryContainer) -> Array[ItemInstance]
get_filter_summary() -> Dictionary
has_active_filters() -> bool
save_as_preset(preset_name: String)
remove_preset(preset_name: String) -> bool
get_preset_names() -> Array[String]
```

## InventoryUI (Control)

Main inventory user interface component.

### Properties
```gdscript
@export var grid_columns: int
@export var slot_size: Vector2
@export var slot_spacing: int
@export var show_container_tabs: bool
@export var show_filter_bar: bool
@export var show_sort_options: bool
```

### Signals
```gdscript
signal slot_clicked(container_id: String, slot_index: int, button: int)
signal slot_double_clicked(container_id: String, slot_index: int)
signal slot_right_clicked(container_id: String, slot_index: int)
signal item_moved(from_container: String, from_slot: int, to_container: String, to_slot: int)
signal inventory_closed()
```

### Methods
```gdscript
display_container(container: InventoryContainer, container_id: String = "")
set_grid_columns(columns: int)
set_slot_size(size: Vector2)
close_inventory()
open_inventory()
toggle_inventory()
get_current_container() -> InventoryContainer
remove_container(container_id: String)
update_status(message: String)
```

## ItemSlot (Control)

Individual inventory slot that can hold and display an item.

### Properties
```gdscript
var item: ItemInstance
var container: InventoryContainer
var slot_index: int
@export var slot_size: Vector2
@export var show_quantity: bool
@export var show_durability: bool
@export var show_rarity_border: bool
@export var allow_drag: bool
@export var allow_drop: bool
```

### Signals
```gdscript
signal item_clicked(slot: ItemSlot, button: int)
signal item_double_clicked(slot: ItemSlot)
signal item_right_clicked(slot: ItemSlot)
signal drag_started(slot: ItemSlot)
signal drag_ended(slot: ItemSlot)
signal drop_received(slot: ItemSlot, data: Variant)
```

### Methods
```gdscript
set_item(new_item: ItemInstance)
update_display()
set_selected(selected: bool)
set_highlighted(highlighted: bool)
get_tooltip_text() -> String
is_empty() -> bool
has_item() -> bool
clear()
get_item_count() -> int
can_stack_with(other_item: ItemInstance) -> bool
get_stack_space() -> int
configure_slot(config: Dictionary)
```

## ItemTooltip (Control)

Rich tooltip system for displaying detailed item information.

### Properties
```gdscript
@export var show_delay: float
@export var hide_delay: float
@export var follow_mouse: bool
@export var offset_from_mouse: Vector2
@export var max_width: float
@export var enable_comparisons: bool
@export var show_item_stats: bool
@export var show_item_description: bool
```

### Signals
```gdscript
signal tooltip_shown(item: ItemInstance)
signal tooltip_hidden()
```

### Methods
```gdscript
show_for_item(item: ItemInstance, delay: bool = true)
hide_tooltip(delay: bool = true)
set_comparison_item(item: ItemInstance)
clear_comparison()
set_tooltip_style(style: Dictionary)
is_showing() -> bool
get_current_item() -> ItemInstance
```

## InventoryEvents (Singleton)

Comprehensive event system for inventory operations.

### Enums
```gdscript
enum EventType { ITEM_ADDED, ITEM_REMOVED, ITEM_MOVED, ITEM_USED, ITEM_EQUIPPED, ITEM_UNEQUIPPED, ITEM_CRAFTED, ITEM_DESTROYED, CONTAINER_CREATED, CONTAINER_DESTROYED, INVENTORY_LOADED, INVENTORY_SAVED, FILTER_CHANGED, SORT_CHANGED }
```

### Methods
```gdscript
register_listener(event_type: EventType, callback: Callable) -> bool
unregister_listener(event_type: EventType, callback: Callable) -> bool
register_global_listener(callback: Callable) -> bool
unregister_global_listener(callback: Callable) -> bool
emit_event(event_type: EventType, data: Dictionary = {}, source: String = "") -> InventoryEvent
start_batch_processing()
end_batch_processing()
get_events_by_type(event_type: EventType) -> Array[InventoryEvent]
get_events_in_range(start_time: float, end_time: float) -> Array[InventoryEvent]
get_recent_events(count: int = 10) -> Array[InventoryEvent]
get_event_statistics() -> Dictionary
get_total_events() -> int
clear_history()
clear_statistics()
set_max_history_size(size: int)
export_history() -> Dictionary
import_history(data: Dictionary)
```

### Convenience Methods
```gdscript
emit_item_added(item: ItemInstance, container_id: String, slot_index: int = -1)
emit_item_removed(item: ItemInstance, container_id: String, slot_index: int = -1)
emit_item_moved(item: ItemInstance, from_container: String, from_slot: int, to_container: String, to_slot: int)
emit_item_used(item: ItemInstance, user_id: String = "", context: Dictionary = {})
emit_item_equipped(item: ItemInstance, equipment_slot: String, character_id: String = "")
emit_item_unequipped(item: ItemInstance, equipment_slot: String, character_id: String = "")
emit_item_crafted(crafted_item: ItemInstance, recipe_id: String, materials: Array[ItemInstance] = [])
emit_item_destroyed(item: ItemInstance, reason: String = "")
```

## PerformanceManager (Node)

Performance optimization system for AAA inventory performance.

### Properties
```gdscript
@export var enable_object_pooling: bool
@export var enable_performance_monitoring: bool
@export var max_pool_size: int
@export var memory_warning_threshold_mb: float
@export var frame_time_warning_threshold_ms: float
```

### Signals
```gdscript
signal performance_warning(metric: String, value: float, threshold: float)
signal memory_pressure_detected(usage_mb: float)
```

### Methods
```gdscript
get_pooled_item_slot() -> ItemSlot
return_item_slot_to_pool(slot: ItemSlot)
get_pooled_tooltip() -> ItemTooltip
return_tooltip_to_pool(tooltip: ItemTooltip)
get_pooled_ui_element(type: String) -> Control
return_ui_element_to_pool(element: Control, type: String)
mark_container_dirty(container: InventoryContainer)
get_performance_metrics() -> Dictionary
get_performance_report() -> Dictionary
set_object_pooling_enabled(enabled: bool)
set_performance_monitoring_enabled(enabled: bool)
set_memory_warning_threshold(threshold_mb: float)
set_frame_time_warning_threshold(threshold_ms: float)
reset_performance_metrics()
```
