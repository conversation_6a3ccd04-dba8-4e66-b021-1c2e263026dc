class_name ItemData
extends Resource

## Base class for all item definitions in the inventory system
## This resource defines the static properties of an item type

enum ItemType {
	WEAPON,
	ARMOR,
	CONSUMABLE,
	MATERIAL,
	TOOL,
	QUEST,
	MISC
}

enum ItemRarity {
	COMMON,
	UNCOMMON,
	RARE,
	EPIC,
	LEGENDARY,
	MYTHIC
}

## Unique identifier for this item type
@export var id: String = ""

## Display name of the item
@export var name: String = ""

## Detailed description of the item
@export_multiline var description: String = ""

## Icon texture for UI display
@export var icon: Texture2D

## Item type category
@export var type: ItemType = ItemType.MISC

## Item rarity level
@export var rarity: ItemRarity = ItemRarity.COMMON

## Maximum number of items that can stack in a single slot
@export var max_stack_size: int = 1

## Base value/price of the item
@export var value: int = 0

## Whether this item can be dropped/discarded
@export var can_drop: bool = true

## Whether this item can be traded
@export var can_trade: bool = true

## Whether this item can be sold to vendors
@export var can_sell: bool = true

## Tags for categorization and filtering
@export var tags: PackedStringArray = []

## Custom properties for item-specific data
@export var custom_properties: Dictionary = {}

func _init():
	resource_name = "ItemData"

## Get the rarity color for UI display
func get_rarity_color() -> Color:
	match rarity:
		ItemRarity.COMMON:
			return Color.WHITE
		ItemRarity.UNCOMMON:
			return Color.GREEN
		ItemRarity.RARE:
			return Color.BLUE
		ItemRarity.EPIC:
			return Color.PURPLE
		ItemRarity.LEGENDARY:
			return Color.ORANGE
		ItemRarity.MYTHIC:
			return Color.RED
		_:
			return Color.WHITE

## Get the type name as string
func get_type_name() -> String:
	return ItemType.keys()[type]

## Get the rarity name as string
func get_rarity_name() -> String:
	return ItemRarity.keys()[rarity]

## Check if item has a specific tag
func has_tag(tag: String) -> bool:
	return tag in tags

## Validate item data integrity
func is_valid() -> bool:
	if id.is_empty():
		push_error("ItemData: ID cannot be empty")
		return false
	if name.is_empty():
		push_error("ItemData: Name cannot be empty")
		return false
	if max_stack_size <= 0:
		push_error("ItemData: Max stack size must be greater than 0")
		return false
	return true

## Get formatted tooltip text
func get_tooltip_text() -> String:
	var text = "[b]%s[/b]\n" % name
	text += "[color=%s]%s[/color]\n" % [get_rarity_color().to_html(), get_rarity_name()]
	text += "\n%s\n" % description
	text += "\nType: %s" % get_type_name()
	if value > 0:
		text += "\nValue: %d" % value
	return text
