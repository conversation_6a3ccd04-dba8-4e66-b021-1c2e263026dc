[res://demo/demo_scene.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 286,
"scroll_position": 271.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://addons/inventory_system/core/performance_manager.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://addons/inventory_system/core/inventory_container.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 266,
"scroll_position": 249.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
