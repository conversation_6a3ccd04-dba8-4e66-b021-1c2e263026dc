[res://demo/demo_scene.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 286,
"scroll_position": 251.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://addons/inventory_system/core/performance_manager.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 140,
"scroll_position": 140.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
