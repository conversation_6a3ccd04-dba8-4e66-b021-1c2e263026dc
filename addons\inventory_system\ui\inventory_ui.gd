class_name InventoryUI
extends Control

## Main inventory user interface component
## Manages the display of inventory containers with grid layout and interactions

signal slot_clicked(container_id: String, slot_index: int, button: int)
signal slot_double_clicked(container_id: String, slot_index: int)
signal slot_right_clicked(container_id: String, slot_index: int)
signal item_moved(from_container: String, from_slot: int, to_container: String, to_slot: int)
signal inventory_closed()

## UI Configuration
@export var grid_columns: int = 6
@export var slot_size: Vector2 = Vector2(64, 64)
@export var slot_spacing: int = 4
@export var show_container_tabs: bool = true
@export var show_filter_bar: bool = true
@export var show_sort_options: bool = true

## Container management
var displayed_containers: Dictionary = {}
var current_container_id: String = ""
var item_filter: ItemFilter

## UI Components
@onready var main_panel: Panel
@onready var tab_container: TabContainer
@onready var filter_bar: HBoxContainer
@onready var sort_options: OptionButton
@onready var search_field: LineEdit
@onready var grid_container: GridContainer
@onready var status_bar: HBoxContainer

## Slot management
var slot_grid: Array[Array] = []
var selected_slot: ItemSlot

func _ready():
	_setup_ui()
	_setup_filter()
	_connect_signals()

## Setup the main UI structure
func _setup_ui():
	# Main panel
	main_panel = Panel.new()
	main_panel.name = "MainPanel"
	main_panel.anchors_preset = Control.PRESET_FULL_RECT
	add_child(main_panel)
	
	var vbox = VBoxContainer.new()
	vbox.anchors_preset = Control.PRESET_FULL_RECT
	vbox.add_theme_constant_override("separation", 8)
	main_panel.add_child(vbox)
	
	# Filter bar
	if show_filter_bar:
		_create_filter_bar(vbox)
	
	# Tab container for multiple inventories
	if show_container_tabs:
		tab_container = TabContainer.new()
		tab_container.name = "TabContainer"
		tab_container.size_flags_vertical = Control.SIZE_EXPAND_FILL
		vbox.add_child(tab_container)
	
	# Grid container for slots
	_create_grid_container(vbox if not show_container_tabs else null)
	
	# Status bar
	_create_status_bar(vbox)

## Create filter bar
func _create_filter_bar(parent: Control):
	filter_bar = HBoxContainer.new()
	filter_bar.name = "FilterBar"
	parent.add_child(filter_bar)
	
	# Search field
	search_field = LineEdit.new()
	search_field.name = "SearchField"
	search_field.placeholder_text = "Search items..."
	search_field.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	filter_bar.add_child(search_field)
	
	# Sort options
	if show_sort_options:
		sort_options = OptionButton.new()
		sort_options.name = "SortOptions"
		sort_options.add_item("Name")
		sort_options.add_item("Type")
		sort_options.add_item("Rarity")
		sort_options.add_item("Value")
		sort_options.add_item("Quantity")
		filter_bar.add_child(sort_options)

## Create grid container
func _create_grid_container(parent: Control):
	var scroll_container = ScrollContainer.new()
	scroll_container.name = "ScrollContainer"
	scroll_container.size_flags_vertical = Control.SIZE_EXPAND_FILL
	
	if parent:
		parent.add_child(scroll_container)
	else:
		# Add to current tab
		var current_tab = _get_or_create_tab("Inventory")
		current_tab.add_child(scroll_container)
	
	grid_container = GridContainer.new()
	grid_container.name = "GridContainer"
	grid_container.columns = grid_columns
	grid_container.add_theme_constant_override("h_separation", slot_spacing)
	grid_container.add_theme_constant_override("v_separation", slot_spacing)
	scroll_container.add_child(grid_container)

## Create status bar
func _create_status_bar(parent: Control):
	status_bar = HBoxContainer.new()
	status_bar.name = "StatusBar"
	parent.add_child(status_bar)
	
	var status_label = Label.new()
	status_label.name = "StatusLabel"
	status_label.text = "Ready"
	status_bar.add_child(status_label)

## Setup item filter
func _setup_filter():
	item_filter = ItemFilter.new()
	item_filter.filter_changed.connect(_on_filter_changed)

## Connect signals
func _connect_signals():
	if search_field:
		search_field.text_changed.connect(_on_search_text_changed)
	
	if sort_options:
		sort_options.item_selected.connect(_on_sort_option_selected)
	
	if tab_container:
		tab_container.tab_changed.connect(_on_tab_changed)

## Display a container
func display_container(container: InventoryContainer, container_id: String = ""):
	if not container:
		push_error("InventoryUI: Cannot display null container")
		return
	
	var id = container_id if not container_id.is_empty() else container.container_id
	displayed_containers[id] = container
	
	if show_container_tabs:
		_create_container_tab(container, id)
	else:
		current_container_id = id
		_refresh_grid()

## Create a tab for a container
func _create_container_tab(container: InventoryContainer, container_id: String):
	var tab = _get_or_create_tab(container.container_name)
	
	# Create grid for this container
	var scroll = ScrollContainer.new()
	scroll.size_flags_vertical = Control.SIZE_EXPAND_FILL
	tab.add_child(scroll)
	
	var grid = GridContainer.new()
	grid.columns = grid_columns
	grid.add_theme_constant_override("h_separation", slot_spacing)
	grid.add_theme_constant_override("v_separation", slot_spacing)
	scroll.add_child(grid)
	
	_populate_grid(grid, container, container_id)

## Get or create a tab
func _get_or_create_tab(tab_name: String) -> Control:
	if not tab_container:
		return null
	
	# Check if tab already exists
	for i in range(tab_container.get_tab_count()):
		if tab_container.get_tab_title(i) == tab_name:
			return tab_container.get_tab_control(i)
	
	# Create new tab
	var tab = VBoxContainer.new()
	tab.name = tab_name
	tab_container.add_child(tab)
	tab_container.set_tab_title(tab_container.get_tab_count() - 1, tab_name)
	
	return tab

## Populate grid with container slots
func _populate_grid(grid: GridContainer, container: InventoryContainer, container_id: String):
	# Clear existing slots
	for child in grid.get_children():
		child.queue_free()
	
	# Create slots
	for i in range(container.max_slots):
		var slot = ItemSlot.new()
		slot.container = container
		slot.slot_index = i
		slot.configure_slot({
			"slot_size": slot_size,
			"show_quantity": true,
			"show_durability": true,
			"show_rarity_border": true
		})
		
		# Connect slot signals
		slot.item_clicked.connect(_on_slot_clicked.bind(container_id, i))
		slot.item_double_clicked.connect(_on_slot_double_clicked.bind(container_id, i))
		slot.item_right_clicked.connect(_on_slot_right_clicked.bind(container_id, i))
		slot.drop_received.connect(_on_slot_drop_received.bind(container_id, i))
		
		# Set item if exists
		var item = container.get_item_at_slot(i)
		if item:
			slot.set_item(item)
		
		grid.add_child(slot)

## Refresh the current grid
func _refresh_grid():
	if current_container_id.is_empty():
		return
	
	var container = displayed_containers.get(current_container_id)
	if not container:
		return
	
	if show_container_tabs:
		# Refresh current tab
		var current_tab_index = tab_container.current_tab
		if current_tab_index >= 0:
			var tab = tab_container.get_tab_control(current_tab_index)
			var scroll = tab.get_child(0) as ScrollContainer
			var grid = scroll.get_child(0) as GridContainer
			_populate_grid(grid, container, current_container_id)
	else:
		_populate_grid(grid_container, container, current_container_id)

## Handle slot click
func _on_slot_clicked(container_id: String, slot_index: int, slot: ItemSlot, button: int):
	# Update selection
	if selected_slot:
		selected_slot.set_selected(false)
	
	selected_slot = slot
	slot.set_selected(true)
	
	slot_clicked.emit(container_id, slot_index, button)

## Handle slot double click
func _on_slot_double_clicked(container_id: String, slot_index: int, slot: ItemSlot):
	slot_double_clicked.emit(container_id, slot_index)

## Handle slot right click
func _on_slot_right_clicked(container_id: String, slot_index: int, slot: ItemSlot):
	slot_right_clicked.emit(container_id, slot_index)

## Handle slot drop
func _on_slot_drop_received(container_id: String, slot_index: int, slot: ItemSlot, data: Variant):
	if not data is Dictionary:
		return
	
	var drop_data = data as Dictionary
	var source_container_id = drop_data.get("source_container", {}).container_id if drop_data.get("source_container") else ""
	var source_slot_index = drop_data.get("source_index", -1)
	
	if source_container_id.is_empty() or source_slot_index < 0:
		return
	
	item_moved.emit(source_container_id, source_slot_index, container_id, slot_index)

## Handle search text change
func _on_search_text_changed(new_text: String):
	item_filter.set_name_filter(new_text)

## Handle sort option selection
func _on_sort_option_selected(index: int):
	var sort_criteria = ["name", "type", "rarity", "value", "quantity"][index]
	item_filter.set_sort_criteria(sort_criteria)

## Handle tab change
func _on_tab_changed(tab_index: int):
	if tab_container and tab_index >= 0:
		var tab_title = tab_container.get_tab_title(tab_index)
		# Find container by name
		for container_id in displayed_containers:
			var container = displayed_containers[container_id]
			if container.container_name == tab_title:
				current_container_id = container_id
				break

## Handle filter change
func _on_filter_changed():
	_apply_filter()

## Apply current filter to displayed items
func _apply_filter():
	if current_container_id.is_empty():
		return
	
	var container = displayed_containers.get(current_container_id)
	if not container:
		return
	
	# Get filtered items
	var filtered_items = item_filter.filter_container(container)
	
	# Update grid to show only filtered items
	_update_grid_with_filtered_items(filtered_items)

## Update grid with filtered items
func _update_grid_with_filtered_items(filtered_items: Array[ItemInstance]):
	# This is a simplified implementation
	# In a full implementation, you'd want to show/hide slots based on filter
	_refresh_grid()

## Set grid columns
func set_grid_columns(columns: int):
	grid_columns = columns
	if grid_container:
		grid_container.columns = columns

## Set slot size
func set_slot_size(size: Vector2):
	slot_size = size
	_refresh_grid()

## Close inventory
func close_inventory():
	visible = false
	inventory_closed.emit()

## Open inventory
func open_inventory():
	visible = true

## Toggle inventory visibility
func toggle_inventory():
	if visible:
		close_inventory()
	else:
		open_inventory()

## Get current container
func get_current_container() -> InventoryContainer:
	return displayed_containers.get(current_container_id)

## Remove container display
func remove_container(container_id: String):
	if container_id in displayed_containers:
		displayed_containers.erase(container_id)
		
		if show_container_tabs and tab_container:
			# Find and remove tab
			for i in range(tab_container.get_tab_count()):
				var container = displayed_containers.get(container_id)
				if container and tab_container.get_tab_title(i) == container.container_name:
					tab_container.remove_child(tab_container.get_tab_control(i))
					break

## Update status
func update_status(message: String):
	if status_bar:
		var status_label = status_bar.get_node("StatusLabel") as Label
		if status_label:
			status_label.text = message
