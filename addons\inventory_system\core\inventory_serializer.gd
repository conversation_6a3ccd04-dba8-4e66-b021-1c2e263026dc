class_name InventorySerializer
extends RefCounted

## Advanced serialization system for inventory data with validation and versioning
## Handles save/load operations with data integrity checks and migration support

const CURRENT_VERSION = "1.0.0"
const SUPPORTED_VERSIONS = ["1.0.0"]

## Validation errors
enum ValidationError {
	NONE,
	INVALID_FORMAT,
	UNSUPPORTED_VERSION,
	CORRUPTED_DATA,
	MISSING_REQUIRED_FIELDS,
	INVALID_ITEM_DATA
}

## Serialize complete inventory system to dictionary
static func serialize_inventory_system(inventory_manager) -> Dictionary:
	var data = {
		"version": CURRENT_VERSION,
		"timestamp": Time.get_unix_time_from_system(),
		"checksum": "",
		"metadata": {
			"game_version": ProjectSettings.get_setting("application/config/version", "1.0.0"),
			"save_count": 1,
			"play_time": 0.0
		},
		"containers": {},
		"global_data": {}
	}
	
	# Serialize all containers
	for container_id in inventory_manager.containers:
		var container = inventory_manager.containers[container_id]
		data.containers[container_id] = serialize_container(container)
	
	# Add checksum for data integrity
	data.checksum = _calculate_checksum(data)
	
	return data

## Serialize a single container
static func serialize_container(container: InventoryContainer) -> Dictionary:
	var data = {
		"container_type": container.container_type,
		"container_name": container.container_name,
		"max_slots": container.max_slots,
		"allowed_types": container.allowed_types,
		"allowed_rarities": container.allowed_rarities,
		"required_tags": container.required_tags,
		"forbidden_tags": container.forbidden_tags,
		"allow_auto_sort": container.allow_auto_sort,
		"allow_stacking": container.allow_stacking,
		"container_id": container.container_id,
		"slots": []
	}
	
	# Serialize each slot
	for i in range(container.max_slots):
		var item = container.slots[i]
		if item:
			data.slots.append(serialize_item_instance(item))
		else:
			data.slots.append(null)
	
	return data

## Serialize an item instance
static func serialize_item_instance(item: ItemInstance) -> Dictionary:
	return {
		"item_id": item.item_data.id,
		"quantity": item.quantity,
		"durability": item.durability,
		"unique_id": item.unique_id,
		"modifications": item.modifications.duplicate(true),
		"instance_properties": item.instance_properties.duplicate(true),
		"created_timestamp": item.created_timestamp,
		"modified_timestamp": item.modified_timestamp
	}

## Deserialize inventory system from dictionary
static func deserialize_inventory_system(data: Dictionary, inventory_manager) -> ValidationError:
	var validation_result = validate_save_data(data)
	if validation_result != ValidationError.NONE:
		return validation_result
	
	# Clear existing containers (except player inventory structure)
	var player_inventory = inventory_manager.get_container("player_inventory")
	if player_inventory:
		player_inventory.clear()
	
	# Deserialize containers
	var containers_data = data.get("containers", {})
	for container_id in containers_data:
		var container_data = containers_data[container_id]
		var container = inventory_manager.get_container(container_id)
		
		if container:
			var result = deserialize_container(container_data, container, inventory_manager.item_database)
			if result != ValidationError.NONE:
				push_warning("Failed to deserialize container: " + container_id)
		else:
			# Create new container if it doesn't exist
			container = create_container_from_data(container_data)
			if container:
				inventory_manager.register_container(container, container_id)
				deserialize_container(container_data, container, inventory_manager.item_database)
	
	return ValidationError.NONE

## Deserialize a container from data
static func deserialize_container(data: Dictionary, container: InventoryContainer, item_database: ItemDatabase) -> ValidationError:
	# Update container properties
	container.container_name = data.get("container_name", container.container_name)
	container.max_slots = data.get("max_slots", container.max_slots)
	container.allowed_types = data.get("allowed_types", container.allowed_types)
	container.allowed_rarities = data.get("allowed_rarities", container.allowed_rarities)
	container.required_tags = data.get("required_tags", container.required_tags)
	container.forbidden_tags = data.get("forbidden_tags", container.forbidden_tags)
	container.allow_auto_sort = data.get("allow_auto_sort", container.allow_auto_sort)
	container.allow_stacking = data.get("allow_stacking", container.allow_stacking)
	
	# Resize container if needed
	container.resize(container.max_slots)
	
	# Deserialize slots
	var slots_data = data.get("slots", [])
	for i in range(mini(slots_data.size(), container.max_slots)):
		var slot_data = slots_data[i]
		if slot_data:
			var item = deserialize_item_instance(slot_data, item_database)
			if item:
				container.slots[i] = item
			else:
				push_warning("Failed to deserialize item in slot %d" % i)
	
	return ValidationError.NONE

## Deserialize an item instance
static func deserialize_item_instance(data: Dictionary, item_database: ItemDatabase) -> ItemInstance:
	var item_id = data.get("item_id", "")
	var item_data = item_database.get_item(item_id)
	
	if not item_data:
		push_error("Item not found in database: " + item_id)
		return null
	
	var item = ItemInstance.new(item_data, data.get("quantity", 1))
	item.durability = data.get("durability", 1.0)
	item.unique_id = data.get("unique_id", item.unique_id)
	item.modifications = data.get("modifications", {})
	item.instance_properties = data.get("instance_properties", {})
	item.created_timestamp = data.get("created_timestamp", Time.get_unix_time_from_system())
	item.modified_timestamp = data.get("modified_timestamp", Time.get_unix_time_from_system())
	
	return item

## Create a container from serialized data
static func create_container_from_data(data: Dictionary) -> InventoryContainer:
	var container = InventoryContainer.new()
	container.container_type = data.get("container_type", InventoryContainer.ContainerType.STORAGE_CONTAINER)
	container.container_name = data.get("container_name", "Container")
	container.max_slots = data.get("max_slots", 20)
	container.allowed_types = data.get("allowed_types", [])
	container.allowed_rarities = data.get("allowed_rarities", [])
	container.required_tags = data.get("required_tags", [])
	container.forbidden_tags = data.get("forbidden_tags", [])
	container.allow_auto_sort = data.get("allow_auto_sort", true)
	container.allow_stacking = data.get("allow_stacking", true)
	container.container_id = data.get("container_id", container.container_id)
	
	return container

## Validate save data integrity
static func validate_save_data(data: Dictionary) -> ValidationError:
	# Check if data is a dictionary
	if not data is Dictionary:
		return ValidationError.INVALID_FORMAT
	
	# Check version
	var version = data.get("version", "")
	if not version in SUPPORTED_VERSIONS:
		return ValidationError.UNSUPPORTED_VERSION
	
	# Check required fields
	var required_fields = ["version", "timestamp", "containers"]
	for field in required_fields:
		if not field in data:
			return ValidationError.MISSING_REQUIRED_FIELDS
	
	# Validate checksum if present
	var stored_checksum = data.get("checksum", "")
	if not stored_checksum.is_empty():
		var data_copy = data.duplicate(true)
		data_copy.erase("checksum")
		var calculated_checksum = _calculate_checksum(data_copy)
		if stored_checksum != calculated_checksum:
			return ValidationError.CORRUPTED_DATA
	
	return ValidationError.NONE

## Calculate checksum for data integrity
static func _calculate_checksum(data: Dictionary) -> String:
	var json_string = JSON.stringify(data)
	return json_string.md5_text()

## Save inventory data to file with compression
static func save_to_file(data: Dictionary, file_path: String, compress: bool = true) -> bool:
	var file = FileAccess.open(file_path, FileAccess.WRITE)
	if not file:
		push_error("InventorySerializer: Could not open file for writing: " + file_path)
		return false
	
	var json_string = JSON.stringify(data)
	
	if compress:
		var compressed_data = json_string.to_utf8_buffer().compress(FileAccess.COMPRESSION_GZIP)
		file.store_32(compressed_data.size())
		file.store_buffer(compressed_data)
	else:
		file.store_string(json_string)
	
	file.close()
	return true

## Load inventory data from file with decompression
static func load_from_file(file_path: String, compressed: bool = true) -> Dictionary:
	if not FileAccess.file_exists(file_path):
		push_error("InventorySerializer: File not found: " + file_path)
		return {}
	
	var file = FileAccess.open(file_path, FileAccess.READ)
	if not file:
		push_error("InventorySerializer: Could not open file for reading: " + file_path)
		return {}
	
	var json_string: String
	
	if compressed:
		var compressed_size = file.get_32()
		var compressed_data = file.get_buffer(compressed_size)
		var decompressed_data = compressed_data.decompress(FileAccess.COMPRESSION_GZIP)
		json_string = decompressed_data.get_string_from_utf8()
	else:
		json_string = file.get_as_text()
	
	file.close()
	
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	
	if parse_result != OK:
		push_error("InventorySerializer: Failed to parse JSON: " + json.get_error_message())
		return {}
	
	return json.data

## Get validation error message
static func get_validation_error_message(error: ValidationError) -> String:
	match error:
		ValidationError.NONE:
			return "No error"
		ValidationError.INVALID_FORMAT:
			return "Invalid save file format"
		ValidationError.UNSUPPORTED_VERSION:
			return "Unsupported save file version"
		ValidationError.CORRUPTED_DATA:
			return "Save file data is corrupted"
		ValidationError.MISSING_REQUIRED_FIELDS:
			return "Save file is missing required fields"
		ValidationError.INVALID_ITEM_DATA:
			return "Invalid item data in save file"
		_:
			return "Unknown error"
