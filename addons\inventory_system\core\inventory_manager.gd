extends Node

## Singleton manager for the entire inventory system
## Handles item database, multiple containers, and high-level operations

signal inventory_system_ready()
signal container_registered(container: InventoryContainer)
signal container_unregistered(container_id: String)
signal item_transferred(item: ItemInstance, from_container: String, to_container: String)

## The item database containing all item definitions
var item_database: ItemDatabase

## Dictionary of all registered inventory containers
var containers: Dictionary = {}

## Default player inventory container
var player_inventory: InventoryContainer

## Whether the system has been initialized
var is_initialized: bool = false

## Configuration settings
var config: Dictionary = {
	"auto_save_enabled": true,
	"auto_save_interval": 30.0,
	"max_containers": 100,
	"debug_logging": false
}

## Auto-save timer
var auto_save_timer: Timer

func _ready():
	name = "InventoryManager"
	_initialize_system()

## Initialize the inventory system
func _initialize_system():
	print("InventoryManager: Initializing inventory system...")
	
	# Create item database
	item_database = ItemDatabase.new()
	
	# Load default item data
	_load_default_items()
	
	# Create default player inventory
	_create_default_player_inventory()
	
	# Setup auto-save if enabled
	if config.auto_save_enabled:
		_setup_auto_save()
	
	is_initialized = true
	inventory_system_ready.emit()
	print("InventoryManager: Inventory system ready")

## Load default items from JSON file
func _load_default_items():
	var default_items_path = "res://addons/inventory_system/data/default_items.json"
	if FileAccess.file_exists(default_items_path):
		item_database.load_from_json(default_items_path)
	else:
		print("InventoryManager: No default items file found, creating empty database")

## Create the default player inventory
func _create_default_player_inventory():
	player_inventory = InventoryContainer.new()
	player_inventory.container_type = InventoryContainer.ContainerType.PLAYER_INVENTORY
	player_inventory.container_name = "Player Inventory"
	player_inventory.max_slots = 30
	register_container(player_inventory, "player_inventory")

## Setup auto-save functionality
func _setup_auto_save():
	auto_save_timer = Timer.new()
	auto_save_timer.wait_time = config.auto_save_interval
	auto_save_timer.timeout.connect(_auto_save)
	auto_save_timer.autostart = true
	add_child(auto_save_timer)

## Register a container with the manager
func register_container(container: InventoryContainer, container_id: String = "") -> String:
	if not container:
		push_error("InventoryManager: Cannot register null container")
		return ""
	
	if containers.size() >= config.max_containers:
		push_error("InventoryManager: Maximum number of containers reached")
		return ""
	
	var id = container_id if not container_id.is_empty() else container.container_id
	
	if id in containers:
		push_warning("InventoryManager: Container with ID '%s' already exists, overwriting" % id)
	
	containers[id] = container
	container_registered.emit(container)
	
	if config.debug_logging:
		print("InventoryManager: Registered container '%s'" % id)
	
	return id

## Unregister a container
func unregister_container(container_id: String) -> bool:
	if not container_id in containers:
		push_warning("InventoryManager: Container '%s' not found" % container_id)
		return false
	
	containers.erase(container_id)
	container_unregistered.emit(container_id)
	
	if config.debug_logging:
		print("InventoryManager: Unregistered container '%s'" % container_id)
	
	return true

## Get a container by ID
func get_container(container_id: String) -> InventoryContainer:
	return containers.get(container_id, null)

## Get all registered containers
func get_all_containers() -> Dictionary:
	return containers.duplicate()

## Create a new item instance from item ID
func create_item(item_id: String, quantity: int = 1) -> ItemInstance:
	var item_data = item_database.get_item(item_id)
	if not item_data:
		push_error("InventoryManager: Item '%s' not found in database" % item_id)
		return null
	
	return ItemInstance.new(item_data, quantity)

## Add an item to a specific container
func add_item_to_container(item: ItemInstance, container_id: String) -> bool:
	var container = get_container(container_id)
	if not container:
		push_error("InventoryManager: Container '%s' not found" % container_id)
		return false
	
	return container.add_item(item)

## Add an item to the player inventory
func add_item_to_player(item: ItemInstance) -> bool:
	return add_item_to_container(item, "player_inventory")

## Create and add an item to player inventory
func give_item_to_player(item_id: String, quantity: int = 1) -> bool:
	var item = create_item(item_id, quantity)
	if not item:
		return false
	
	return add_item_to_player(item)

## Transfer an item between containers
func transfer_item(
	item: ItemInstance, 
	from_container_id: String, 
	to_container_id: String,
	from_slot: int = -1,
	to_slot: int = -1
) -> bool:
	var from_container = get_container(from_container_id)
	var to_container = get_container(to_container_id)
	
	if not from_container or not to_container:
		push_error("InventoryManager: Invalid container IDs for transfer")
		return false
	
	# Remove from source container
	var removed_item: ItemInstance
	if from_slot >= 0:
		removed_item = from_container.remove_item_from_slot(from_slot)
	else:
		# Find and remove the item
		for i in range(from_container.max_slots):
			if from_container.slots[i] == item:
				removed_item = from_container.remove_item_from_slot(i)
				break
	
	if not removed_item:
		return false
	
	# Add to destination container
	var success: bool
	if to_slot >= 0:
		success = to_container.add_item_to_slot(removed_item, to_slot)
	else:
		success = to_container.add_item(removed_item)
	
	if success:
		item_transferred.emit(removed_item, from_container_id, to_container_id)
		return true
	else:
		# Return item to original container if transfer failed
		from_container.add_item(removed_item)
		return false

## Get total count of a specific item across all containers
func get_total_item_count(item_id: String) -> int:
	var total = 0
	for container in containers.values():
		for item in container.get_all_items():
			if item.item_data.id == item_id:
				total += item.quantity
	return total

## Find all instances of an item across containers
func find_item_instances(item_id: String) -> Array:
	var instances: Array = []
	
	for container_id in containers:
		var container = containers[container_id]
		for i in range(container.max_slots):
			var item = container.slots[i]
			if item and item.item_data.id == item_id:
				instances.append({
					"item": item,
					"container_id": container_id,
					"slot_index": i
				})
	
	return instances

## Remove a specific quantity of an item from all containers
func consume_item(item_id: String, quantity: int) -> bool:
	var instances = find_item_instances(item_id)
	var remaining = quantity
	
	for instance_data in instances:
		if remaining <= 0:
			break
		
		var item = instance_data.item
		var container = get_container(instance_data.container_id)
		var slot_index = instance_data.slot_index
		
		if item.quantity <= remaining:
			remaining -= item.quantity
			container.remove_item_from_slot(slot_index)
		else:
			item.set_quantity(item.quantity - remaining)
			remaining = 0
	
	return remaining == 0

## Save all inventory data
func save_inventory_data(file_path: String = "user://inventory_save.json") -> bool:
	var save_data = {
		"version": "1.0",
		"timestamp": Time.get_unix_time_from_system(),
		"containers": {}
	}
	
	for container_id in containers:
		var container = containers[container_id]
		save_data.containers[container_id] = _serialize_container(container)
	
	var file = FileAccess.open(file_path, FileAccess.WRITE)
	if not file:
		push_error("InventoryManager: Could not open save file: " + file_path)
		return false
	
	file.store_string(JSON.stringify(save_data))
	file.close()
	
	if config.debug_logging:
		print("InventoryManager: Saved inventory data to " + file_path)
	
	return true

## Load inventory data
func load_inventory_data(file_path: String = "user://inventory_save.json") -> bool:
	if not FileAccess.file_exists(file_path):
		push_warning("InventoryManager: Save file not found: " + file_path)
		return false
	
	var file = FileAccess.open(file_path, FileAccess.READ)
	if not file:
		push_error("InventoryManager: Could not open save file: " + file_path)
		return false
	
	var json_text = file.get_as_text()
	file.close()
	
	var json = JSON.new()
	var parse_result = json.parse(json_text)
	
	if parse_result != OK:
		push_error("InventoryManager: Failed to parse save file: " + json.get_error_message())
		return false
	
	var save_data = json.data
	if not save_data is Dictionary:
		push_error("InventoryManager: Invalid save file format")
		return false
	
	# Load containers
	var containers_data = save_data.get("containers", {})
	for container_id in containers_data:
		var container = get_container(container_id)
		if container:
			_deserialize_container(container, containers_data[container_id])
	
	if config.debug_logging:
		print("InventoryManager: Loaded inventory data from " + file_path)
	
	return true

## Serialize a container to dictionary
func _serialize_container(container: InventoryContainer) -> Dictionary:
	var data = {
		"container_type": container.container_type,
		"max_slots": container.max_slots,
		"container_name": container.container_name,
		"slots": []
	}
	
	for item in container.slots:
		if item:
			data.slots.append(item.to_dict())
		else:
			data.slots.append(null)
	
	return data

## Deserialize container data
func _deserialize_container(container: InventoryContainer, data: Dictionary):
	container.clear()
	
	var slots_data = data.get("slots", [])
	for i in range(mini(slots_data.size(), container.max_slots)):
		var slot_data = slots_data[i]
		if slot_data:
			var item = ItemInstance.from_dict(slot_data, item_database)
			if item:
				container.slots[i] = item

## Auto-save callback
func _auto_save():
	if is_initialized:
		save_inventory_data()

## Get system statistics
func get_system_stats() -> Dictionary:
	var stats = {
		"containers_count": containers.size(),
		"total_items": 0,
		"database_stats": item_database.get_stats()
	}
	
	for container in containers.values():
		stats.total_items += container.get_all_items().size()
	
	return stats
