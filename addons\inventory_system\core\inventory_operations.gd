class_name InventoryOperations
extends RefCounted

## Advanced inventory operations handler with validation and error handling
## Provides high-level operations for complex inventory manipulations

enum OperationResult {
	SUCCESS,
	FAILED_INVALID_ITEM,
	FAILED_INVALID_CONTAINER,
	FAILED_INVALID_SLOT,
	FAILED_CONTAINER_FULL,
	FAILED_ITEM_RESTRICTIONS,
	FAILED_INSUFFICIENT_QUANTITY,
	FAILED_CANNOT_STACK,
	FAILED_OPERATION_NOT_ALLOWED
}

## Operation context for tracking complex operations
class OperationContext:
	var operation_id: String
	var timestamp: float
	var operations: Array[Dictionary] = []
	var can_rollback: bool = true
	
	func _init():
		operation_id = "op_%d_%d" % [Time.get_unix_time_from_system(), randi()]
		timestamp = Time.get_unix_time_from_system()

## Add item with smart placement (tries stacking first, then empty slots)
static func add_item_smart(container: InventoryContainer, item: ItemInstance) -> OperationResult:
	if not container or not item:
		return OperationResult.FAILED_INVALID_ITEM
	
	if not container.can_add_item(item):
		return OperationResult.FAILED_ITEM_RESTRICTIONS
	
	var remaining_quantity = item.quantity
	var original_item = item.duplicate()
	
	# First pass: try to stack with existing items
	if container.allow_stacking:
		for i in range(container.max_slots):
			if remaining_quantity <= 0:
				break
			
			var existing_item = container.slots[i]
			if existing_item and existing_item.can_stack_with(item):
				var space = existing_item.get_stack_space()
				if space > 0:
					var to_add = mini(remaining_quantity, space)
					existing_item.add_quantity(to_add)
					remaining_quantity -= to_add
	
	# Second pass: place remaining items in empty slots
	if remaining_quantity > 0:
		for i in range(container.max_slots):
			if remaining_quantity <= 0:
				break
			
			if container.slots[i] == null:
				var stack_size = mini(remaining_quantity, item.item_data.max_stack_size)
				var new_item = original_item.duplicate()
				new_item.set_quantity(stack_size)
				container.slots[i] = new_item
				remaining_quantity -= stack_size
	
	if remaining_quantity > 0:
		return OperationResult.FAILED_CONTAINER_FULL
	
	container.container_changed.emit()
	return OperationResult.SUCCESS

## Remove specific quantity of an item from container
static func remove_item_quantity(container: InventoryContainer, item_id: String, quantity: int) -> Dictionary:
	var result = {
		"result": OperationResult.SUCCESS,
		"removed_items": [],
		"remaining_quantity": quantity
	}
	
	if not container:
		result.result = OperationResult.FAILED_INVALID_CONTAINER
		return result
	
	var remaining = quantity
	
	# Find and remove items
	for i in range(container.max_slots):
		if remaining <= 0:
			break
		
		var item = container.slots[i]
		if item and item.item_data.id == item_id:
			if item.quantity <= remaining:
				# Remove entire stack
				result.removed_items.append(container.remove_item_from_slot(i))
				remaining -= item.quantity
			else:
				# Split stack
				var removed = container.remove_quantity_from_slot(i, remaining)
				if removed:
					result.removed_items.append(removed)
					remaining = 0
	
	result.remaining_quantity = remaining
	if remaining > 0:
		result.result = OperationResult.FAILED_INSUFFICIENT_QUANTITY
	
	return result

## Move item between containers with validation
static func move_item_between_containers(
	from_container: InventoryContainer,
	to_container: InventoryContainer,
	from_slot: int,
	to_slot: int = -1
) -> OperationResult:
	
	if not from_container or not to_container:
		return OperationResult.FAILED_INVALID_CONTAINER
	
	if not from_container._is_valid_slot(from_slot):
		return OperationResult.FAILED_INVALID_SLOT
	
	var item = from_container.get_item_at_slot(from_slot)
	if not item:
		return OperationResult.FAILED_INVALID_ITEM
	
	if not to_container.can_add_item(item):
		return OperationResult.FAILED_ITEM_RESTRICTIONS
	
	# Remove from source
	var removed_item = from_container.remove_item_from_slot(from_slot)
	if not removed_item:
		return OperationResult.FAILED_INVALID_ITEM
	
	# Add to destination
	var success: bool
	if to_slot >= 0:
		success = to_container.add_item_to_slot(removed_item, to_slot)
	else:
		success = to_container.add_item(removed_item)
	
	if not success:
		# Rollback: return item to original container
		from_container.add_item_to_slot(removed_item, from_slot)
		return OperationResult.FAILED_CONTAINER_FULL
	
	return OperationResult.SUCCESS

## Split item stack into multiple stacks
static func split_item_stack(container: InventoryContainer, slot_index: int, split_quantity: int) -> OperationResult:
	if not container or not container._is_valid_slot(slot_index):
		return OperationResult.FAILED_INVALID_SLOT
	
	var item = container.get_item_at_slot(slot_index)
	if not item:
		return OperationResult.FAILED_INVALID_ITEM
	
	if split_quantity <= 0 or split_quantity >= item.quantity:
		return OperationResult.FAILED_INSUFFICIENT_QUANTITY
	
	var split_item = item.split(split_quantity)
	if not split_item:
		return OperationResult.FAILED_OPERATION_NOT_ALLOWED
	
	# Try to place split item in container
	if not container.add_item(split_item):
		# Rollback: merge items back
		item.add_quantity(split_item.quantity)
		return OperationResult.FAILED_CONTAINER_FULL
	
	return OperationResult.SUCCESS

## Merge compatible item stacks
static func merge_item_stacks(container: InventoryContainer, slot1: int, slot2: int) -> OperationResult:
	if not container:
		return OperationResult.FAILED_INVALID_CONTAINER
	
	if not container._is_valid_slot(slot1) or not container._is_valid_slot(slot2):
		return OperationResult.FAILED_INVALID_SLOT
	
	var item1 = container.get_item_at_slot(slot1)
	var item2 = container.get_item_at_slot(slot2)
	
	if not item1 or not item2:
		return OperationResult.FAILED_INVALID_ITEM
	
	if not item1.can_stack_with(item2):
		return OperationResult.FAILED_CANNOT_STACK
	
	var space = item1.get_stack_space()
	if space <= 0:
		return OperationResult.FAILED_CANNOT_STACK
	
	var to_merge = mini(item2.quantity, space)
	item1.add_quantity(to_merge)
	
	if to_merge >= item2.quantity:
		container.remove_item_from_slot(slot2)
	else:
		item2.remove_quantity(to_merge)
	
	return OperationResult.SUCCESS

## Auto-sort container by criteria
static func auto_sort_container(container: InventoryContainer, sort_criteria: String = "type_rarity") -> OperationResult:
	if not container or not container.allow_auto_sort:
		return OperationResult.FAILED_OPERATION_NOT_ALLOWED
	
	var items = container.get_all_items()
	if items.is_empty():
		return OperationResult.SUCCESS
	
	# Clear container
	container.clear()
	
	# Sort items based on criteria
	match sort_criteria:
		"type_rarity":
			items.sort_custom(_sort_by_type_and_rarity)
		"rarity":
			items.sort_custom(_sort_by_rarity)
		"value":
			items.sort_custom(_sort_by_value)
		"name":
			items.sort_custom(_sort_by_name)
		"quantity":
			items.sort_custom(_sort_by_quantity)
		_:
			items.sort_custom(_sort_by_type_and_rarity)
	
	# Re-add items to container
	for item in items:
		if not container.add_item(item):
			# This shouldn't happen, but handle gracefully
			push_warning("Failed to re-add item during sorting: " + item.item_data.name)
	
	return OperationResult.SUCCESS

## Compact container by merging stackable items
static func compact_container(container: InventoryContainer) -> OperationResult:
	if not container or not container.allow_stacking:
		return OperationResult.FAILED_OPERATION_NOT_ALLOWED
	
	var items_by_id: Dictionary = {}
	
	# Group items by ID and properties
	for i in range(container.max_slots):
		var item = container.slots[i]
		if item:
			var key = _get_stack_key(item)
			if not key in items_by_id:
				items_by_id[key] = []
			items_by_id[key].append({"item": item, "slot": i})
	
	# Clear container
	container.clear()
	
	# Merge and re-add items
	for key in items_by_id:
		var item_group = items_by_id[key]
		var total_quantity = 0
		var base_item = item_group[0].item
		
		# Calculate total quantity
		for item_data in item_group:
			total_quantity += item_data.item.quantity
		
		# Create new stacks
		while total_quantity > 0:
			var stack_size = mini(total_quantity, base_item.item_data.max_stack_size)
			var new_item = base_item.duplicate()
			new_item.set_quantity(stack_size)
			
			if not container.add_item(new_item):
				push_warning("Failed to add item during compacting")
				break
			
			total_quantity -= stack_size
	
	return OperationResult.SUCCESS

## Batch operation for multiple inventory operations
static func execute_batch_operation(operations: Array[Dictionary]) -> Dictionary:
	var context = OperationContext.new()
	var results = []
	var all_successful = true
	
	for operation in operations:
		var result = _execute_single_operation(operation, context)
		results.append(result)
		
		if result.result != OperationResult.SUCCESS:
			all_successful = false
			if context.can_rollback:
				_rollback_operations(context)
				break
	
	return {
		"success": all_successful,
		"results": results,
		"context": context
	}

## Execute a single operation
static func _execute_single_operation(operation: Dictionary, context: OperationContext) -> Dictionary:
	var result = {"result": OperationResult.FAILED_OPERATION_NOT_ALLOWED}
	
	match operation.get("type", ""):
		"add_item":
			result.result = add_item_smart(operation.container, operation.item)
		"remove_item":
			var remove_result = remove_item_quantity(operation.container, operation.item_id, operation.quantity)
			result = remove_result
		"move_item":
			result.result = move_item_between_containers(
				operation.from_container,
				operation.to_container,
				operation.from_slot,
				operation.get("to_slot", -1)
			)
		_:
			push_warning("Unknown operation type: " + str(operation.get("type", "")))
	
	context.operations.append({
		"operation": operation,
		"result": result,
		"timestamp": Time.get_unix_time_from_system()
	})
	
	return result

## Rollback operations (simplified implementation)
static func _rollback_operations(context: OperationContext):
	push_warning("Operation rollback not fully implemented")
	# TODO: Implement proper rollback mechanism

## Sorting functions
static func _sort_by_type_and_rarity(a: ItemInstance, b: ItemInstance) -> bool:
	if a.item_data.type != b.item_data.type:
		return a.item_data.type < b.item_data.type
	return a.item_data.rarity > b.item_data.rarity

static func _sort_by_rarity(a: ItemInstance, b: ItemInstance) -> bool:
	return a.item_data.rarity > b.item_data.rarity

static func _sort_by_value(a: ItemInstance, b: ItemInstance) -> bool:
	return a.get_total_value() > b.get_total_value()

static func _sort_by_name(a: ItemInstance, b: ItemInstance) -> bool:
	return a.item_data.name < b.item_data.name

static func _sort_by_quantity(a: ItemInstance, b: ItemInstance) -> bool:
	return a.quantity > b.quantity

## Get unique key for stacking items
static func _get_stack_key(item: ItemInstance) -> String:
	return "%s_%s_%s" % [
		item.item_data.id,
		str(item.durability),
		str(item.modifications.hash())
	]

## Get operation result message
static func get_operation_result_message(result: OperationResult) -> String:
	match result:
		OperationResult.SUCCESS:
			return "Operation completed successfully"
		OperationResult.FAILED_INVALID_ITEM:
			return "Invalid item"
		OperationResult.FAILED_INVALID_CONTAINER:
			return "Invalid container"
		OperationResult.FAILED_INVALID_SLOT:
			return "Invalid slot"
		OperationResult.FAILED_CONTAINER_FULL:
			return "Container is full"
		OperationResult.FAILED_ITEM_RESTRICTIONS:
			return "Item restrictions prevent this operation"
		OperationResult.FAILED_INSUFFICIENT_QUANTITY:
			return "Insufficient quantity"
		OperationResult.FAILED_CANNOT_STACK:
			return "Items cannot be stacked"
		OperationResult.FAILED_OPERATION_NOT_ALLOWED:
			return "Operation not allowed"
		_:
			return "Unknown error"
