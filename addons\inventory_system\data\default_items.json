{"version": "1.0.0", "items": [{"id": "sword_iron", "name": "Iron Sword", "description": "A sturdy iron sword with a sharp blade. Reliable in combat.", "type": "WEAPON", "rarity": "COMMON", "max_stack_size": 1, "value": 150, "can_drop": true, "can_trade": true, "can_sell": true, "tags": ["melee", "sword", "metal"], "custom_properties": {"damage": 25, "attack_speed": 1.2, "durability_max": 100}}, {"id": "sword_steel", "name": "Steel Sword", "description": "A well-crafted steel sword with superior balance and sharpness.", "type": "WEAPON", "rarity": "UNCOMMON", "max_stack_size": 1, "value": 300, "can_drop": true, "can_trade": true, "can_sell": true, "tags": ["melee", "sword", "metal"], "custom_properties": {"damage": 35, "attack_speed": 1.3, "durability_max": 150}}, {"id": "bow_hunting", "name": "Hunting Bow", "description": "A simple wooden bow perfect for hunting small game.", "type": "WEAPON", "rarity": "COMMON", "max_stack_size": 1, "value": 80, "can_drop": true, "can_trade": true, "can_sell": true, "tags": ["ranged", "bow", "wood"], "custom_properties": {"damage": 20, "range": 50, "durability_max": 80}}, {"id": "armor_leather_chest", "name": "Leather Chestplate", "description": "Basic leather armor that provides minimal protection.", "type": "ARMOR", "rarity": "COMMON", "max_stack_size": 1, "value": 100, "can_drop": true, "can_trade": true, "can_sell": true, "tags": ["armor", "chest", "leather"], "custom_properties": {"defense": 15, "durability_max": 60, "slot": "chest"}}, {"id": "armor_iron_chest", "name": "Iron Chestplate", "description": "Sturdy iron armor that offers good protection against attacks.", "type": "ARMOR", "rarity": "UNCOMMON", "max_stack_size": 1, "value": 250, "can_drop": true, "can_trade": true, "can_sell": true, "tags": ["armor", "chest", "metal"], "custom_properties": {"defense": 30, "durability_max": 120, "slot": "chest"}}, {"id": "potion_health", "name": "Health Potion", "description": "A red potion that restores health when consumed.", "type": "CONSUMABLE", "rarity": "COMMON", "max_stack_size": 10, "value": 25, "can_drop": true, "can_trade": true, "can_sell": true, "tags": ["potion", "healing", "consumable"], "custom_properties": {"heal_amount": 50, "instant": true}}, {"id": "potion_mana", "name": "<PERSON><PERSON>", "description": "A blue potion that restores magical energy.", "type": "CONSUMABLE", "rarity": "COMMON", "max_stack_size": 10, "value": 30, "can_drop": true, "can_trade": true, "can_sell": true, "tags": ["potion", "mana", "consumable"], "custom_properties": {"mana_amount": 75, "instant": true}}, {"id": "material_iron_ore", "name": "Iron Ore", "description": "Raw iron ore that can be smelted into iron ingots.", "type": "MATERIAL", "rarity": "COMMON", "max_stack_size": 50, "value": 5, "can_drop": true, "can_trade": true, "can_sell": true, "tags": ["ore", "metal", "crafting"], "custom_properties": {"smelt_result": "material_iron_ingot", "smelt_time": 5.0}}, {"id": "material_iron_ingot", "name": "Iron Ingot", "description": "Refined iron ready for crafting weapons and armor.", "type": "MATERIAL", "rarity": "COMMON", "max_stack_size": 25, "value": 15, "can_drop": true, "can_trade": true, "can_sell": true, "tags": ["ingot", "metal", "crafting"], "custom_properties": {"tier": 1}}, {"id": "material_wood", "name": "<PERSON>", "description": "Common wood suitable for basic crafting and construction.", "type": "MATERIAL", "rarity": "COMMON", "max_stack_size": 64, "value": 2, "can_drop": true, "can_trade": true, "can_sell": true, "tags": ["wood", "crafting", "building"], "custom_properties": {"burn_time": 10.0}}, {"id": "tool_pickaxe_iron", "name": "Iron Pickaxe", "description": "A sturdy iron pickaxe for mining stone and ores.", "type": "TOOL", "rarity": "COMMON", "max_stack_size": 1, "value": 120, "can_drop": true, "can_trade": true, "can_sell": true, "tags": ["tool", "pickaxe", "mining"], "custom_properties": {"mining_power": 2, "mining_speed": 1.5, "durability_max": 200}}, {"id": "gem_ruby", "name": "<PERSON>", "description": "A precious red gemstone that radiates with inner fire.", "type": "MISC", "rarity": "RARE", "max_stack_size": 5, "value": 500, "can_drop": true, "can_trade": true, "can_sell": true, "tags": ["gem", "precious", "enchanting"], "custom_properties": {"enchant_power": 15, "element": "fire"}}, {"id": "gem_emerald", "name": "Emerald", "description": "A brilliant green gemstone pulsing with natural energy.", "type": "MISC", "rarity": "RARE", "max_stack_size": 5, "value": 450, "can_drop": true, "can_trade": true, "can_sell": true, "tags": ["gem", "precious", "enchanting"], "custom_properties": {"enchant_power": 12, "element": "nature"}}, {"id": "quest_ancient_key", "name": "Ancient Key", "description": "An ornate key covered in mysterious runes. It seems important.", "type": "QUEST", "rarity": "LEGENDARY", "max_stack_size": 1, "value": 0, "can_drop": false, "can_trade": false, "can_sell": false, "tags": ["quest", "key", "ancient"], "custom_properties": {"quest_id": "ancient_temple", "unique": true}}, {"id": "food_bread", "name": "Bread", "description": "Fresh baked bread that restores hunger and provides energy.", "type": "CONSUMABLE", "rarity": "COMMON", "max_stack_size": 20, "value": 8, "can_drop": true, "can_trade": true, "can_sell": true, "tags": ["food", "consumable", "hunger"], "custom_properties": {"hunger_restore": 25, "spoil_time": 300.0}}]}