class_name ItemTooltip
extends Control

## Advanced tooltip system for displaying detailed item information
## Supports rich text, comparisons, and dynamic positioning

signal tooltip_shown(item: ItemInstance)
signal tooltip_hidden()

## Tooltip configuration
@export var show_delay: float = 0.5
@export var hide_delay: float = 0.1
@export var follow_mouse: bool = true
@export var offset_from_mouse: Vector2 = Vector2(10, 10)
@export var max_width: float = 300.0
@export var enable_comparisons: bool = true
@export var show_item_stats: bool = true
@export var show_item_description: bool = true

## UI Components
@onready var background_panel: Panel
@onready var main_container: VBoxContainer
@onready var header_container: HBoxContainer
@onready var item_icon: TextureRect
@onready var item_name_label: RichTextLabel
@onready var rarity_label: Label
@onready var description_label: RichTextLabel
@onready var stats_container: VBoxContainer
@onready var comparison_container: VBoxContainer

## State management
var current_item: ItemInstance
var comparison_item: ItemInstance
var is_visible: bool = false
var show_timer: Timer
var hide_timer: Timer
var target_position: Vector2

## Styling
var rarity_colors: Dictionary = {
	ItemData.ItemRarity.COMMON: Color.WHITE,
	ItemData.ItemRarity.UNCOMMON: Color.GREEN,
	ItemData.ItemRarity.RARE: Color.BLUE,
	ItemData.ItemRarity.EPIC: Color.PURPLE,
	ItemData.ItemRarity.LEGENDARY: Color.ORANGE,
	ItemData.ItemRarity.MYTHIC: Color.RED
}

func _ready():
	_setup_ui()
	_setup_timers()
	visible = false
	mouse_filter = Control.MOUSE_FILTER_IGNORE

## Setup UI components
func _setup_ui():
	# Background panel
	background_panel = Panel.new()
	background_panel.name = "BackgroundPanel"
	background_panel.anchors_preset = Control.PRESET_FULL_RECT
	add_child(background_panel)
	
	# Main container
	main_container = VBoxContainer.new()
	main_container.name = "MainContainer"
	main_container.anchors_preset = Control.PRESET_FULL_RECT
	main_container.add_theme_constant_override("separation", 8)
	background_panel.add_child(main_container)
	
	# Header container
	header_container = HBoxContainer.new()
	header_container.name = "HeaderContainer"
	main_container.add_child(header_container)
	
	# Item icon
	item_icon = TextureRect.new()
	item_icon.name = "ItemIcon"
	item_icon.custom_minimum_size = Vector2(32, 32)
	item_icon.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
	item_icon.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
	header_container.add_child(item_icon)
	
	# Name and rarity container
	var name_container = VBoxContainer.new()
	name_container.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	header_container.add_child(name_container)
	
	# Item name
	item_name_label = RichTextLabel.new()
	item_name_label.name = "ItemNameLabel"
	item_name_label.custom_minimum_size.y = 24
	item_name_label.fit_content = true
	item_name_label.bbcode_enabled = true
	item_name_label.scroll_active = false
	name_container.add_child(item_name_label)
	
	# Rarity label
	rarity_label = Label.new()
	rarity_label.name = "RarityLabel"
	name_container.add_child(rarity_label)
	
	# Description
	if show_item_description:
		description_label = RichTextLabel.new()
		description_label.name = "DescriptionLabel"
		description_label.fit_content = true
		description_label.bbcode_enabled = true
		description_label.scroll_active = false
		main_container.add_child(description_label)
	
	# Stats container
	if show_item_stats:
		stats_container = VBoxContainer.new()
		stats_container.name = "StatsContainer"
		main_container.add_child(stats_container)
	
	# Comparison container
	if enable_comparisons:
		comparison_container = VBoxContainer.new()
		comparison_container.name = "ComparisonContainer"
		comparison_container.visible = false
		main_container.add_child(comparison_container)

## Setup timers
func _setup_timers():
	show_timer = Timer.new()
	show_timer.name = "ShowTimer"
	show_timer.wait_time = show_delay
	show_timer.one_shot = true
	show_timer.timeout.connect(_show_tooltip)
	add_child(show_timer)
	
	hide_timer = Timer.new()
	hide_timer.name = "HideTimer"
	hide_timer.wait_time = hide_delay
	hide_timer.one_shot = true
	hide_timer.timeout.connect(_hide_tooltip)
	add_child(hide_timer)

## Show tooltip for item
func show_for_item(item: ItemInstance, delay: bool = true):
	if current_item == item and is_visible:
		return
	
	current_item = item
	hide_timer.stop()
	
	if delay and show_delay > 0:
		show_timer.start()
	else:
		_show_tooltip()

## Hide tooltip
func hide_tooltip(delay: bool = true):
	show_timer.stop()
	
	if delay and hide_delay > 0:
		hide_timer.start()
	else:
		_hide_tooltip()

## Set comparison item
func set_comparison_item(item: ItemInstance):
	comparison_item = item
	if is_visible:
		_update_content()

## Clear comparison
func clear_comparison():
	comparison_item = null
	if comparison_container:
		comparison_container.visible = false

## Show tooltip immediately
func _show_tooltip():
	if not current_item:
		return
	
	_update_content()
	_update_position()
	
	visible = true
	is_visible = true
	tooltip_shown.emit(current_item)

## Hide tooltip immediately
func _hide_tooltip():
	visible = false
	is_visible = false
	current_item = null
	tooltip_hidden.emit()

## Update tooltip content
func _update_content():
	if not current_item or not current_item.item_data:
		return
	
	var item_data = current_item.item_data
	
	# Update icon
	if item_icon and item_data.icon:
		item_icon.texture = item_data.icon
	
	# Update name with rarity color
	if item_name_label:
		var color = rarity_colors.get(item_data.rarity, Color.WHITE)
		item_name_label.text = "[color=%s][b]%s[/b][/color]" % [color.to_html(), item_data.name]
	
	# Update rarity
	if rarity_label:
		rarity_label.text = item_data.get_rarity_name()
		rarity_label.modulate = rarity_colors.get(item_data.rarity, Color.WHITE)
	
	# Update description
	if description_label and show_item_description:
		description_label.text = item_data.description
	
	# Update stats
	if stats_container and show_item_stats:
		_update_stats()
	
	# Update comparison
	if comparison_container and enable_comparisons and comparison_item:
		_update_comparison()
	
	# Resize to fit content
	_resize_to_content()

## Update item stats display
func _update_stats():
	if not stats_container:
		return
	
	# Clear existing stats
	for child in stats_container.get_children():
		child.queue_free()
	
	var item_data = current_item.item_data
	
	# Basic stats
	_add_stat_line("Type", item_data.get_type_name())
	
	if current_item.quantity > 1:
		_add_stat_line("Quantity", str(current_item.quantity))
	
	if item_data.value > 0:
		_add_stat_line("Value", str(current_item.get_total_value()) + " gold")
	
	if current_item.durability < 1.0:
		var durability_text = "%.1f%%" % (current_item.durability * 100)
		var color = Color.GREEN
		if current_item.durability < 0.3:
			color = Color.RED
		elif current_item.durability < 0.6:
			color = Color.YELLOW
		
		_add_stat_line("Durability", durability_text, color)
	
	# Custom properties
	for property in item_data.custom_properties:
		var value = item_data.custom_properties[property]
		_add_stat_line(property.capitalize(), str(value))
	
	# Tags
	if not item_data.tags.is_empty():
		_add_stat_line("Tags", ", ".join(item_data.tags))

## Add a stat line
func _add_stat_line(label: String, value: String, color: Color = Color.WHITE):
	var stat_container = HBoxContainer.new()
	stats_container.add_child(stat_container)
	
	var label_node = Label.new()
	label_node.text = label + ":"
	label_node.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	stat_container.add_child(label_node)
	
	var value_node = Label.new()
	value_node.text = value
	value_node.modulate = color
	value_node.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
	stat_container.add_child(value_node)

## Update comparison display
func _update_comparison():
	if not comparison_container or not comparison_item:
		return
	
	comparison_container.visible = true
	
	# Clear existing comparison
	for child in comparison_container.get_children():
		child.queue_free()
	
	# Add comparison header
	var header = Label.new()
	header.text = "Compared to: " + comparison_item.item_data.name
	header.add_theme_color_override("font_color", Color.YELLOW)
	comparison_container.add_child(header)
	
	# Compare stats
	_compare_custom_properties()

## Compare custom properties
func _compare_custom_properties():
	var current_props = current_item.item_data.custom_properties
	var comparison_props = comparison_item.item_data.custom_properties
	
	var all_props = {}
	for prop in current_props:
		all_props[prop] = true
	for prop in comparison_props:
		all_props[prop] = true
	
	for prop in all_props:
		var current_value = current_props.get(prop, 0)
		var comparison_value = comparison_props.get(prop, 0)
		
		if current_value != comparison_value:
			var difference = current_value - comparison_value
			var color = Color.GREEN if difference > 0 else Color.RED
			var sign = "+" if difference > 0 else ""
			
			_add_comparison_line(prop.capitalize(), sign + str(difference), color)

## Add comparison line
func _add_comparison_line(label: String, difference: String, color: Color):
	var comp_container = HBoxContainer.new()
	comparison_container.add_child(comp_container)
	
	var label_node = Label.new()
	label_node.text = label + ":"
	label_node.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	comp_container.add_child(label_node)
	
	var diff_node = Label.new()
	diff_node.text = difference
	diff_node.modulate = color
	diff_node.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
	comp_container.add_child(diff_node)

## Resize tooltip to fit content
func _resize_to_content():
	# Force update layout
	main_container.queue_sort()
	await get_tree().process_frame
	
	# Calculate required size
	var content_size = main_container.get_combined_minimum_size()
	content_size.x = minf(content_size.x, max_width)
	
	# Add padding
	content_size += Vector2(16, 16)
	
	# Set size
	custom_minimum_size = content_size
	size = content_size

## Update tooltip position
func _update_position():
	if not follow_mouse:
		return
	
	var mouse_pos = get_global_mouse_position()
	var viewport_size = get_viewport().size
	
	# Calculate position with offset
	target_position = mouse_pos + offset_from_mouse
	
	# Ensure tooltip stays within viewport
	if target_position.x + size.x > viewport_size.x:
		target_position.x = mouse_pos.x - size.x - offset_from_mouse.x
	
	if target_position.y + size.y > viewport_size.y:
		target_position.y = mouse_pos.y - size.y - offset_from_mouse.y
	
	# Clamp to viewport
	target_position.x = maxf(0, target_position.x)
	target_position.y = maxf(0, target_position.y)
	
	global_position = target_position

## Process mouse movement for following
func _process(delta):
	if is_visible and follow_mouse:
		_update_position()

## Set tooltip style
func set_tooltip_style(style: Dictionary):
	if "max_width" in style:
		max_width = style.max_width
	
	if "show_delay" in style:
		show_delay = style.show_delay
		show_timer.wait_time = show_delay
	
	if "hide_delay" in style:
		hide_delay = style.hide_delay
		hide_timer.wait_time = hide_delay
	
	if "follow_mouse" in style:
		follow_mouse = style.follow_mouse
	
	if "offset" in style:
		offset_from_mouse = style.offset

## Check if tooltip is currently showing
func is_showing() -> bool:
	return is_visible

## Get current item being displayed
func get_current_item() -> ItemInstance:
	return current_item
