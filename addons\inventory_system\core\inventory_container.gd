class_name InventoryContainer
extends Resource

## Manages a collection of items with configurable constraints
## Supports different container types like player inventory, storage, equipment slots

signal item_added(item: ItemInstance, slot_index: int)
signal item_removed(item: ItemInstance, slot_index: int)
signal item_moved(item: ItemInstance, from_slot: int, to_slot: int)
signal container_changed()

enum ContainerType {
	PLAYER_INVENTORY,
	STORAGE_CONTAINER,
	EQUIPMENT_SLOTS,
	HOTBAR,
	CRAFTING_GRID,
	LOOT_CONTAINER,
	VENDOR_INVENTORY,
	QUEST_REWARDS
}

## Type of this container
@export var container_type: ContainerType = ContainerType.PLAYER_INVENTORY

## Maximum number of slots in this container
@export var max_slots: int = 20

## Allowed item types (empty array means all types allowed)
@export var allowed_types: Array = []

## Allowed item rarities (empty array means all rarities allowed)
@export var allowed_rarities: Array = []

## Required tags for items to be placed in this container
@export var required_tags: PackedStringArray = []

## Forbidden tags that prevent items from being placed
@export var forbidden_tags: PackedStringArray = []

## Whether items can be automatically sorted
@export var allow_auto_sort: bool = true

## Whether items can be stacked in this container
@export var allow_stacking: bool = true

## Container name for display purposes
@export var container_name: String = "Inventory"

## Array of item instances in each slot (null = empty slot)
var slots: Array = []

## Container unique identifier
var container_id: String

func _init():
	resource_name = "InventoryContainer"
	container_id = _generate_container_id()
	# Initialize with default max_slots, will be resized later if needed
	if max_slots <= 0:
		max_slots = 20  # Default size
	_initialize_slots()

## Generate a unique container ID
func _generate_container_id() -> String:
	return "container_%s_%d" % [ContainerType.keys()[container_type].to_lower(), Time.get_unix_time_from_system()]

## Initialize all slots as empty
func _initialize_slots():
	slots.clear()
	slots.resize(max_slots)
	for i in range(max_slots):
		slots[i] = null

## Resize the container (preserves existing items where possible)
func resize(new_size: int) -> bool:
	if new_size < 0:
		push_error("InventoryContainer: Container size cannot be negative")
		return false
	
	# Check if we're shrinking and have items that would be lost
	if new_size < max_slots:
		for i in range(new_size, max_slots):
			if slots[i] != null:
				push_error("InventoryContainer: Cannot shrink container, slot %d contains items" % i)
				return false
	
	var old_size = max_slots
	max_slots = new_size
	slots.resize(max_slots)

	# Initialize new slots as empty
	for i in range(old_size, max_slots):
		if i < slots.size():
			slots[i] = null
	
	container_changed.emit()
	return true

## Check if an item can be placed in this container
func can_add_item(item: ItemInstance) -> bool:
	if not item or not item.item_data:
		return false
	
	# Check type restrictions
	if not allowed_types.is_empty() and not item.item_data.type in allowed_types:
		return false
	
	# Check rarity restrictions
	if not allowed_rarities.is_empty() and not item.item_data.rarity in allowed_rarities:
		return false
	
	# Check required tags
	for tag in required_tags:
		if not item.item_data.has_tag(tag):
			return false
	
	# Check forbidden tags
	for tag in forbidden_tags:
		if item.item_data.has_tag(tag):
			return false
	
	return true

## Check if an item can be placed in a specific slot
func can_add_item_to_slot(item: ItemInstance, slot_index: int) -> bool:
	if not _is_valid_slot(slot_index):
		return false
	
	if not can_add_item(item):
		return false
	
	var existing_item = slots[slot_index]
	
	# If slot is empty, item can be placed
	if existing_item == null:
		return true
	
	# If slot has item, check if they can stack
	if allow_stacking and existing_item.can_stack_with(item):
		return existing_item.get_stack_space() > 0
	
	return false

## Add an item to the first available slot
func add_item(item: ItemInstance) -> bool:
	if not can_add_item(item):
		return false
	
	# Try to stack with existing items first
	if allow_stacking:
		for i in range(max_slots):
			var existing_item = slots[i]
			if existing_item and existing_item.can_stack_with(item):
				var remaining = existing_item.add_quantity(item.quantity)
				if remaining == 0:
					item_added.emit(item, i)
					container_changed.emit()
					return true
				else:
					item.set_quantity(remaining)
	
	# Find first empty slot
	for i in range(max_slots):
		if slots[i] == null:
			slots[i] = item
			item_added.emit(item, i)
			container_changed.emit()
			return true
	
	return false

## Add an item to a specific slot
func add_item_to_slot(item: ItemInstance, slot_index: int) -> bool:
	if not can_add_item_to_slot(item, slot_index):
		return false
	
	var existing_item = slots[slot_index]
	
	if existing_item == null:
		slots[slot_index] = item
		item_added.emit(item, slot_index)
		container_changed.emit()
		return true
	
	# Stack with existing item
	if allow_stacking and existing_item.can_stack_with(item):
		var remaining = existing_item.add_quantity(item.quantity)
		if remaining == 0:
			item_added.emit(item, slot_index)
			container_changed.emit()
			return true
		else:
			item.set_quantity(remaining)
			return false
	
	return false

## Remove an item from a specific slot
func remove_item_from_slot(slot_index: int) -> ItemInstance:
	if not _is_valid_slot(slot_index) or slots[slot_index] == null:
		return null
	
	var item = slots[slot_index]
	slots[slot_index] = null
	item_removed.emit(item, slot_index)
	container_changed.emit()
	return item

## Remove a specific quantity from a slot
func remove_quantity_from_slot(slot_index: int, quantity: int) -> ItemInstance:
	if not _is_valid_slot(slot_index) or slots[slot_index] == null:
		return null
	
	var item = slots[slot_index]
	if quantity >= item.quantity:
		return remove_item_from_slot(slot_index)
	
	var removed_item = item.split(quantity)
	if removed_item:
		item_removed.emit(removed_item, slot_index)
		container_changed.emit()
	
	return removed_item

## Move an item from one slot to another
func move_item(from_slot: int, to_slot: int) -> bool:
	if not _is_valid_slot(from_slot) or not _is_valid_slot(to_slot):
		return false
	
	if from_slot == to_slot:
		return true
	
	var from_item = slots[from_slot]
	var to_item = slots[to_slot]
	
	if from_item == null:
		return false
	
	# If destination is empty, simple move
	if to_item == null:
		slots[to_slot] = from_item
		slots[from_slot] = null
		item_moved.emit(from_item, from_slot, to_slot)
		container_changed.emit()
		return true
	
	# If items can stack, try to stack them
	if allow_stacking and from_item.can_stack_with(to_item):
		var remaining = to_item.add_quantity(from_item.quantity)
		if remaining == 0:
			slots[from_slot] = null
			item_moved.emit(from_item, from_slot, to_slot)
			container_changed.emit()
			return true
		else:
			from_item.set_quantity(remaining)
			return false
	
	# Swap items
	slots[from_slot] = to_item
	slots[to_slot] = from_item
	item_moved.emit(from_item, from_slot, to_slot)
	item_moved.emit(to_item, to_slot, from_slot)
	container_changed.emit()
	return true

## Get item at specific slot
func get_item_at_slot(slot_index: int) -> ItemInstance:
	if not _is_valid_slot(slot_index):
		return null
	return slots[slot_index]

## Check if a slot is valid
func _is_valid_slot(slot_index: int) -> bool:
	return slot_index >= 0 and slot_index < max_slots and slot_index < slots.size()

## Get all non-empty items
func get_all_items() -> Array:
	var items: Array = []
	for item in slots:
		if item != null:
			items.append(item)
	return items

## Get number of empty slots
func get_empty_slot_count() -> int:
	var count = 0
	for item in slots:
		if item == null:
			count += 1
	return count

## Check if container is full
func is_full() -> bool:
	return get_empty_slot_count() == 0

## Check if container is empty
func is_empty() -> bool:
	return get_empty_slot_count() == max_slots

## Clear all items from container
func clear() -> Array:
	var removed_items: Array = []
	for i in range(max_slots):
		if slots[i] != null:
			removed_items.append(slots[i])
			slots[i] = null
	container_changed.emit()
	return removed_items
