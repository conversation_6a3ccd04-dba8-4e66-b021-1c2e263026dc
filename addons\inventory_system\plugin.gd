@tool
extends EditorPlugin

const AUTOLOAD_NAME = "InventoryManager"
const AUTOLOAD_PATH = "res://addons/inventory_system/core/inventory_manager.gd"

func _enter_tree():
	# Add the autoload when the plugin is enabled
	add_autoload_singleton(AUTOLOAD_NAME, AUTOLOAD_PATH)
	print("AAA Inventory System: Plugin enabled")

func _exit_tree():
	# Remove the autoload when the plugin is disabled
	remove_autoload_singleton(AUTOLOAD_NAME)
	print("AAA Inventory System: Plugin disabled")

func get_plugin_name():
	return "AAA Inventory System"
