list=[{
"base": &"Control",
"class": &"DragDropManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/inventory_system/ui/drag_drop_manager.gd"
}, {
"base": &"Resource",
"class": &"InventoryContainer",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/inventory_system/core/inventory_container.gd"
}, {
"base": &"RefCounted",
"class": &"InventoryEvents",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/inventory_system/core/inventory_events.gd"
}, {
"base": &"RefCounted",
"class": &"InventoryOperations",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/inventory_system/core/inventory_operations.gd"
}, {
"base": &"RefCounted",
"class": &"InventorySerializer",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/inventory_system/core/inventory_serializer.gd"
}, {
"base": &"Control",
"class": &"InventoryUI",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/inventory_system/ui/inventory_ui.gd"
}, {
"base": &"Resource",
"class": &"ItemData",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/inventory_system/core/item_data.gd"
}, {
"base": &"Resource",
"class": &"ItemDatabase",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/inventory_system/core/item_database.gd"
}, {
"base": &"RefCounted",
"class": &"ItemFilter",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/inventory_system/core/item_filter.gd"
}, {
"base": &"RefCounted",
"class": &"ItemInstance",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/inventory_system/core/item_instance.gd"
}, {
"base": &"Control",
"class": &"ItemSlot",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/inventory_system/ui/item_slot.gd"
}, {
"base": &"Control",
"class": &"ItemTooltip",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/inventory_system/ui/item_tooltip.gd"
}, {
"base": &"Node",
"class": &"PerformanceManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/inventory_system/core/performance_manager.gd"
}]
