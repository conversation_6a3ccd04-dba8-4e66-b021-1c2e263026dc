extends Node

## Performance optimization system for AAA inventory performance
## Handles object pooling, efficient updates, memory management, and performance monitoring

signal performance_warning(metric: String, value: float, threshold: float)
signal memory_pressure_detected(usage_mb: float)

## Object pools
var item_slot_pool: Array = []
var tooltip_pool: Array = []
var ui_element_pool: Dictionary = {}

## Performance monitoring
var frame_time_samples: Array = []
var memory_samples: Array = []
var update_time_samples: Array = []

## Configuration
@export var enable_object_pooling: bool = true
@export var enable_performance_monitoring: bool = true
@export var max_pool_size: int = 100
@export var sample_count: int = 60
@export var memory_warning_threshold_mb: float = 500.0
@export var frame_time_warning_threshold_ms: float = 16.67  # 60 FPS

## Update optimization
var dirty_containers: Array = []
var update_batch_size: int = 5
var current_update_index: int = 0

## Memory management
var gc_timer: Timer
var gc_interval: float = 30.0
var last_memory_usage: float = 0.0

## Performance metrics
var performance_metrics: Dictionary = {
	"avg_frame_time": 0.0,
	"avg_memory_usage": 0.0,
	"avg_update_time": 0.0,
	"peak_memory_usage": 0.0,
	"total_pooled_objects": 0,
	"cache_hit_rate": 0.0
}

func _ready():
	name = "PerformanceManager"
	_setup_monitoring()
	_setup_garbage_collection()

## Setup performance monitoring
func _setup_monitoring():
	if not enable_performance_monitoring:
		return
	
	# Initialize sample arrays
	frame_time_samples.resize(sample_count)
	memory_samples.resize(sample_count)
	update_time_samples.resize(sample_count)
	
	frame_time_samples.fill(0.0)
	memory_samples.fill(0.0)
	update_time_samples.fill(0.0)

## Setup garbage collection timer
func _setup_garbage_collection():
	gc_timer = Timer.new()
	gc_timer.wait_time = gc_interval
	gc_timer.timeout.connect(_perform_garbage_collection)
	gc_timer.autostart = true
	add_child(gc_timer)

## Process performance monitoring
func _process(delta):
	if enable_performance_monitoring:
		_update_performance_metrics(delta)
	
	_process_dirty_containers()

## Update performance metrics
func _update_performance_metrics(delta: float):
	var frame_time_ms = delta * 1000.0
	var memory_usage = _get_memory_usage_mb()
	
	# Add samples
	_add_sample(frame_time_samples, frame_time_ms)
	_add_sample(memory_samples, memory_usage)
	
	# Calculate averages
	performance_metrics.avg_frame_time = _calculate_average(frame_time_samples)
	performance_metrics.avg_memory_usage = _calculate_average(memory_samples)
	performance_metrics.peak_memory_usage = maxf(performance_metrics.peak_memory_usage, memory_usage)
	
	# Check thresholds
	if performance_metrics.avg_frame_time > frame_time_warning_threshold_ms:
		performance_warning.emit("frame_time", performance_metrics.avg_frame_time, frame_time_warning_threshold_ms)
	
	if memory_usage > memory_warning_threshold_mb:
		memory_pressure_detected.emit(memory_usage)

## Add sample to circular buffer
func _add_sample(samples: Array, value: float):
	samples[current_update_index % sample_count] = value

## Calculate average of samples
func _calculate_average(samples: Array) -> float:
	var sum = 0.0
	for sample in samples:
		sum += sample
	return sum / samples.size()

## Get memory usage in MB
func _get_memory_usage_mb() -> float:
	# Use a simpler memory estimation since get_static_memory_usage_by_type doesn't exist
	return Performance.get_monitor(Performance.MEMORY_STATIC) / 1024.0 / 1024.0

## Object Pooling System

## Get pooled item slot
func get_pooled_item_slot() -> ItemSlot:
	if not enable_object_pooling:
		return ItemSlot.new()
	
	if item_slot_pool.is_empty():
		return ItemSlot.new()
	
	var slot = item_slot_pool.pop_back()
	slot.visible = true
	slot.clear()
	return slot

## Return item slot to pool
func return_item_slot_to_pool(slot: ItemSlot):
	if not enable_object_pooling or item_slot_pool.size() >= max_pool_size:
		slot.queue_free()
		return
	
	slot.visible = false
	slot.clear()
	slot.get_parent().remove_child(slot)
	item_slot_pool.append(slot)

## Get pooled tooltip
func get_pooled_tooltip() -> ItemTooltip:
	if not enable_object_pooling:
		return ItemTooltip.new()
	
	if tooltip_pool.is_empty():
		return ItemTooltip.new()
	
	var tooltip = tooltip_pool.pop_back()
	tooltip.visible = false
	return tooltip

## Return tooltip to pool
func return_tooltip_to_pool(tooltip: ItemTooltip):
	if not enable_object_pooling or tooltip_pool.size() >= max_pool_size:
		tooltip.queue_free()
		return
	
	tooltip.visible = false
	tooltip.hide_tooltip(false)
	tooltip.get_parent().remove_child(tooltip)
	tooltip_pool.append(tooltip)

## Get pooled UI element
func get_pooled_ui_element(type: String) -> Control:
	if not enable_object_pooling:
		return _create_ui_element(type)
	
	if not type in ui_element_pool:
		ui_element_pool[type] = []
	
	var pool = ui_element_pool[type]
	if pool.is_empty():
		return _create_ui_element(type)
	
	return pool.pop_back()

## Return UI element to pool
func return_ui_element_to_pool(element: Control, type: String):
	if not enable_object_pooling:
		element.queue_free()
		return
	
	if not type in ui_element_pool:
		ui_element_pool[type] = []
	
	var pool = ui_element_pool[type]
	if pool.size() >= max_pool_size:
		element.queue_free()
		return
	
	element.get_parent().remove_child(element)
	pool.append(element)

## Create UI element by type
func _create_ui_element(type: String) -> Control:
	match type:
		"Label":
			return Label.new()
		"Button":
			return Button.new()
		"Panel":
			return Panel.new()
		"VBoxContainer":
			return VBoxContainer.new()
		"HBoxContainer":
			return HBoxContainer.new()
		_:
			return Control.new()

## Update Optimization System

## Mark container as dirty for update
func mark_container_dirty(container: InventoryContainer):
	if container not in dirty_containers:
		dirty_containers.append(container)

## Process dirty containers in batches
func _process_dirty_containers():
	if dirty_containers.is_empty():
		return
	
	var start_time = Time.get_ticks_usec()
	var processed = 0
	
	while processed < update_batch_size and not dirty_containers.is_empty():
		var container = dirty_containers.pop_front()
		_update_container_ui(container)
		processed += 1
	
	var update_time = (Time.get_ticks_usec() - start_time) / 1000.0
	_add_sample(update_time_samples, update_time)
	performance_metrics.avg_update_time = _calculate_average(update_time_samples)

## Update container UI efficiently
func _update_container_ui(container: InventoryContainer):
	# This would be called by the UI system to update only changed slots
	# Implementation depends on the specific UI framework being used
	pass

## Memory Management

## Perform garbage collection
func _perform_garbage_collection():
	# Trim object pools if they're too large
	_trim_pools()
	
	# Clear old performance samples
	_cleanup_old_samples()
	
	# Force garbage collection
	if last_memory_usage > 0:
		var current_usage = _get_memory_usage_mb()
		if current_usage > last_memory_usage * 1.5:  # 50% increase
			print("PerformanceManager: Forcing garbage collection due to memory pressure")
			# Note: Godot doesn't have explicit GC control, but we can clean up our own objects
			_cleanup_internal_caches()
	
	last_memory_usage = _get_memory_usage_mb()

## Trim object pools to reasonable sizes
func _trim_pools():
	var target_size = max_pool_size / 2
	
	# Trim item slot pool
	while item_slot_pool.size() > target_size:
		var slot = item_slot_pool.pop_back()
		slot.queue_free()
	
	# Trim tooltip pool
	while tooltip_pool.size() > target_size:
		var tooltip = tooltip_pool.pop_back()
		tooltip.queue_free()
	
	# Trim UI element pools
	for type in ui_element_pool:
		var pool = ui_element_pool[type]
		while pool.size() > target_size:
			var element = pool.pop_back()
			element.queue_free()

## Cleanup old performance samples
func _cleanup_old_samples():
	# Reset samples if they're getting stale
	current_update_index += 1
	if current_update_index >= sample_count * 10:  # Reset every 10 cycles
		current_update_index = 0

## Cleanup internal caches
func _cleanup_internal_caches():
	# Clear any internal caches that might be holding references
	dirty_containers.clear()

## Performance Monitoring API

## Get current performance metrics
func get_performance_metrics() -> Dictionary:
	performance_metrics.total_pooled_objects = _count_pooled_objects()
	return performance_metrics.duplicate()

## Count total pooled objects
func _count_pooled_objects() -> int:
	var total = item_slot_pool.size() + tooltip_pool.size()
	for pool in ui_element_pool.values():
		total += pool.size()
	return total

## Get detailed performance report
func get_performance_report() -> Dictionary:
	return {
		"metrics": get_performance_metrics(),
		"memory_usage_mb": _get_memory_usage_mb(),
		"pool_sizes": {
			"item_slots": item_slot_pool.size(),
			"tooltips": tooltip_pool.size(),
			"ui_elements": _get_ui_pool_sizes()
		},
		"dirty_containers": dirty_containers.size(),
		"sample_counts": {
			"frame_time": frame_time_samples.size(),
			"memory": memory_samples.size(),
			"update_time": update_time_samples.size()
		}
	}

## Get UI pool sizes
func _get_ui_pool_sizes() -> Dictionary:
	var sizes = {}
	for type in ui_element_pool:
		sizes[type] = ui_element_pool[type].size()
	return sizes

## Configuration API

## Set object pooling enabled
func set_object_pooling_enabled(enabled: bool):
	enable_object_pooling = enabled
	if not enabled:
		_clear_all_pools()

## Set performance monitoring enabled
func set_performance_monitoring_enabled(enabled: bool):
	enable_performance_monitoring = enabled

## Set memory warning threshold
func set_memory_warning_threshold(threshold_mb: float):
	memory_warning_threshold_mb = threshold_mb

## Set frame time warning threshold
func set_frame_time_warning_threshold(threshold_ms: float):
	frame_time_warning_threshold_ms = threshold_ms

## Clear all object pools
func _clear_all_pools():
	for slot in item_slot_pool:
		slot.queue_free()
	item_slot_pool.clear()
	
	for tooltip in tooltip_pool:
		tooltip.queue_free()
	tooltip_pool.clear()
	
	for type in ui_element_pool:
		for element in ui_element_pool[type]:
			element.queue_free()
		ui_element_pool[type].clear()

## Reset performance metrics
func reset_performance_metrics():
	performance_metrics = {
		"avg_frame_time": 0.0,
		"avg_memory_usage": 0.0,
		"avg_update_time": 0.0,
		"peak_memory_usage": 0.0,
		"total_pooled_objects": 0,
		"cache_hit_rate": 0.0
	}
	
	frame_time_samples.fill(0.0)
	memory_samples.fill(0.0)
	update_time_samples.fill(0.0)
	current_update_index = 0
