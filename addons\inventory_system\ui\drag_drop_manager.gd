class_name DragDropManager
extends Control

## Advanced drag and drop system for inventory items
## Handles visual feedback, validation, and smooth interactions

signal drag_started(data: Dictionary)
signal drag_ended(data: Dictionary, success: bool)
signal drop_validated(source_data: Dictionary, target_data: Dictionary)
signal drop_rejected(source_data: Dictionary, target_data: Dictionary, reason: String)

## Drag state management
var is_dragging: bool = false
var drag_data: Dictionary = {}
var drag_preview: Control
var drag_start_position: Vector2
var drag_threshold: float = 5.0

## Visual feedback
var drop_zones: Array = []
var valid_drop_zones: Array = []
var invalid_drop_zones: Array = []

## Configuration
@export var show_drag_preview: bool = true
@export var show_drop_indicators: bool = true
@export var enable_auto_scroll: bool = true
@export var scroll_margin: float = 50.0
@export var scroll_speed: float = 200.0

## Visual styles
var valid_drop_color: Color = Color(0, 1, 0, 0.3)
var invalid_drop_color: Color = Color(1, 0, 0, 0.3)
var neutral_drop_color: Color = Color(1, 1, 1, 0.1)

## Drop validation rules
var validation_rules: Array = []

func _ready():
	name = "DragDropManager"
	set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	mouse_filter = Control.MOUSE_FILTER_IGNORE

## Start drag operation
func start_drag(source_data: Dictionary) -> bool:
	if is_dragging:
		return false
	
	if not _validate_drag_data(source_data):
		return false
	
	is_dragging = true
	drag_data = source_data.duplicate(true)
	drag_start_position = get_global_mouse_position()
	
	# Create drag preview
	if show_drag_preview:
		_create_drag_preview()
	
	# Update drop zones
	_update_drop_zones()
	
	# Enable mouse tracking
	mouse_filter = Control.MOUSE_FILTER_PASS
	
	drag_started.emit(drag_data)
	return true

## End drag operation
func end_drag(success: bool = false):
	if not is_dragging:
		return
	
	is_dragging = false
	mouse_filter = Control.MOUSE_FILTER_IGNORE
	
	# Clean up preview
	if drag_preview:
		drag_preview.queue_free()
		drag_preview = null
	
	# Clear drop zone highlights
	_clear_drop_zone_highlights()
	
	drag_ended.emit(drag_data, success)
	drag_data.clear()

## Handle mouse movement during drag
func _gui_input(event: InputEvent):
	if not is_dragging:
		return
	
	if event is InputEventMouseMotion:
		_handle_drag_motion(event)
	elif event is InputEventMouseButton:
		var mouse_event = event as InputEventMouseButton
		if mouse_event.button_index == MOUSE_BUTTON_LEFT and not mouse_event.pressed:
			_handle_drop()

## Handle drag motion
func _handle_drag_motion(event: InputEventMouseMotion):
	if not is_dragging:
		return
	
	# Update preview position
	if drag_preview:
		drag_preview.global_position = event.global_position - drag_preview.size * 0.5
	
	# Update drop zone validation
	_update_drop_zone_validation(event.global_position)
	
	# Handle auto-scroll
	if enable_auto_scroll:
		_handle_auto_scroll(event.global_position)

## Handle drop attempt
func _handle_drop():
	if not is_dragging:
		return
	
	var drop_position = get_global_mouse_position()
	var target_control = _find_drop_target(drop_position)
	
	if target_control and _can_drop_on_target(target_control):
		var target_data = _get_target_data(target_control)
		if _validate_drop(drag_data, target_data):
			_execute_drop(target_control, target_data)
			end_drag(true)
			return
	
	# Drop failed
	end_drag(false)

## Create drag preview
func _create_drag_preview():
	if not drag_data.has("item") or not show_drag_preview:
		return
	
	var item = drag_data.get("item") as ItemInstance
	if not item:
		return
	
	# Create preview slot
	drag_preview = ItemSlot.new()
	drag_preview.is_drag_preview = true
	drag_preview.set_item(item)
	drag_preview.modulate.a = 0.8
	drag_preview.mouse_filter = Control.MOUSE_FILTER_IGNORE
	
	# Add to scene
	get_tree().current_scene.add_child(drag_preview)
	drag_preview.global_position = get_global_mouse_position() - drag_preview.size * 0.5

## Update drop zones
func _update_drop_zones():
	drop_zones.clear()
	valid_drop_zones.clear()
	invalid_drop_zones.clear()
	
	# Find all potential drop zones
	_find_drop_zones(get_tree().current_scene)

## Find drop zones recursively
func _find_drop_zones(node: Node):
	if node is ItemSlot:
		drop_zones.append(node)
	
	for child in node.get_children():
		_find_drop_zones(child)

## Update drop zone validation
func _update_drop_zone_validation(mouse_position: Vector2):
	valid_drop_zones.clear()
	invalid_drop_zones.clear()
	
	for zone in drop_zones:
		if _is_position_over_control(mouse_position, zone):
			if _can_drop_on_target(zone):
				valid_drop_zones.append(zone)
			else:
				invalid_drop_zones.append(zone)
	
	_update_drop_zone_highlights()

## Update drop zone visual highlights
func _update_drop_zone_highlights():
	if not show_drop_indicators:
		return
	
	# Clear all highlights first
	for zone in drop_zones:
		_set_drop_zone_highlight(zone, Color.TRANSPARENT)
	
	# Highlight valid zones
	for zone in valid_drop_zones:
		_set_drop_zone_highlight(zone, valid_drop_color)
	
	# Highlight invalid zones
	for zone in invalid_drop_zones:
		_set_drop_zone_highlight(zone, invalid_drop_color)

## Set drop zone highlight
func _set_drop_zone_highlight(zone: Control, color: Color):
	if zone.has_method("set_drop_highlight"):
		zone.set_drop_highlight(color)
	else:
		# Fallback: modulate the control
		zone.modulate = Color.WHITE if color.a == 0 else Color(1.2, 1.2, 1.2)

## Clear drop zone highlights
func _clear_drop_zone_highlights():
	for zone in drop_zones:
		_set_drop_zone_highlight(zone, Color.TRANSPARENT)

## Find drop target at position
func _find_drop_target(position: Vector2) -> Control:
	for zone in drop_zones:
		if _is_position_over_control(position, zone):
			return zone
	return null

## Check if position is over control
func _is_position_over_control(position: Vector2, control: Control) -> bool:
	if not control.visible:
		return false
	
	var rect = Rect2(control.global_position, control.size)
	return rect.has_point(position)

## Check if can drop on target
func _can_drop_on_target(target: Control) -> bool:
	if not target:
		return false
	
	# Check if target accepts drops
	if target.has_method("_can_drop_data"):
		return target._can_drop_data(Vector2.ZERO, drag_data)
	
	return false

## Get target data
func _get_target_data(target: Control) -> Dictionary:
	var data = {}
	
	if target is ItemSlot:
		var slot = target as ItemSlot
		data = {
			"type": "item_slot",
			"slot": slot,
			"container": slot.container,
			"slot_index": slot.slot_index,
			"item": slot.item
		}
	
	return data

## Validate drop operation
func _validate_drop(source_data: Dictionary, target_data: Dictionary) -> bool:
	# Run custom validation rules
	for rule in validation_rules:
		if not rule.call(source_data, target_data):
			drop_rejected.emit(source_data, target_data, "Custom validation failed")
			return false
	
	# Basic validation
	if source_data.get("type") != "inventory_item":
		drop_rejected.emit(source_data, target_data, "Invalid source type")
		return false
	
	if target_data.get("type") != "item_slot":
		drop_rejected.emit(source_data, target_data, "Invalid target type")
		return false
	
	drop_validated.emit(source_data, target_data)
	return true

## Execute drop operation
func _execute_drop(target_control: Control, target_data: Dictionary):
	if target_control.has_method("_drop_data"):
		target_control._drop_data(Vector2.ZERO, drag_data)

## Handle auto-scroll
func _handle_auto_scroll(mouse_position: Vector2):
	var viewport = get_viewport()
	if not viewport:
		return
	
	var viewport_rect = Rect2(Vector2.ZERO, viewport.size)
	var scroll_vector = Vector2.ZERO
	
	# Check edges for scrolling
	if mouse_position.x < scroll_margin:
		scroll_vector.x = -scroll_speed
	elif mouse_position.x > viewport_rect.size.x - scroll_margin:
		scroll_vector.x = scroll_speed
	
	if mouse_position.y < scroll_margin:
		scroll_vector.y = -scroll_speed
	elif mouse_position.y > viewport_rect.size.y - scroll_margin:
		scroll_vector.y = scroll_speed
	
	# Apply scrolling to scroll containers
	if scroll_vector != Vector2.ZERO:
		_apply_auto_scroll(scroll_vector)

## Apply auto-scroll to containers
func _apply_auto_scroll(scroll_vector: Vector2):
	# Find scroll containers and apply scrolling
	var scroll_containers = _find_scroll_containers(get_tree().current_scene)
	
	for container in scroll_containers:
		if container.visible:
			container.scroll_horizontal += int(scroll_vector.x * get_process_delta_time())
			container.scroll_vertical += int(scroll_vector.y * get_process_delta_time())

## Find scroll containers
func _find_scroll_containers(node: Node) -> Array:
	var containers: Array = []
	
	if node is ScrollContainer:
		containers.append(node)
	
	for child in node.get_children():
		containers.append_array(_find_scroll_containers(child))
	
	return containers

## Validate drag data
func _validate_drag_data(data: Dictionary) -> bool:
	if not data.has("type"):
		return false
	
	if data.get("type") != "inventory_item":
		return false
	
	if not data.has("item"):
		return false
	
	return true

## Add validation rule
func add_validation_rule(rule: Callable):
	if not rule in validation_rules:
		validation_rules.append(rule)

## Remove validation rule
func remove_validation_rule(rule: Callable):
	var index = validation_rules.find(rule)
	if index >= 0:
		validation_rules.remove_at(index)

## Set visual styles
func set_drop_colors(valid: Color, invalid: Color, neutral: Color = Color.TRANSPARENT):
	valid_drop_color = valid
	invalid_drop_color = invalid
	neutral_drop_color = neutral

## Check if currently dragging
func is_drag_active() -> bool:
	return is_dragging

## Get current drag data
func get_current_drag_data() -> Dictionary:
	return drag_data.duplicate() if is_dragging else {}

## Force end drag (emergency cleanup)
func force_end_drag():
	if is_dragging:
		end_drag(false)

## Set drag threshold
func set_drag_threshold(threshold: float):
	drag_threshold = threshold

## Enable/disable features
func set_feature_enabled(feature: String, enabled: bool):
	match feature:
		"preview":
			show_drag_preview = enabled
		"indicators":
			show_drop_indicators = enabled
		"auto_scroll":
			enable_auto_scroll = enabled
