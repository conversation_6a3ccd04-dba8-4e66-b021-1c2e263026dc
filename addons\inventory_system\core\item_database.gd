class_name ItemDatabase
extends Resource

## Central repository for all item definitions
## Manages loading, caching, and retrieval of item data

signal database_loaded()
signal item_registered(item_data: ItemData)

## Dictionary storing all item data by ID
var _items: Dictionary = {}

## Dictionary storing items by type for quick filtering
var _items_by_type: Dictionary = {}

## Dictionary storing items by rarity for quick filtering
var _items_by_rarity: Dictionary = {}

## Dictionary storing items by tags for quick searching
var _items_by_tag: Dictionary = {}

## Whether the database has been loaded
var is_loaded: bool = false

func _init():
	resource_name = "ItemDatabase"
	_initialize_type_dictionaries()

## Initialize the type and rarity dictionaries
func _initialize_type_dictionaries():
	for type in ItemData.ItemType.values():
		_items_by_type[type] = []
	
	for rarity in ItemData.ItemRarity.values():
		_items_by_rarity[rarity] = []

## Register a new item in the database
func register_item(item_data: ItemData) -> bool:
	if not item_data or not item_data.is_valid():
		push_error("ItemDatabase: Cannot register invalid item data")
		return false
	
	if item_data.id in _items:
		push_warning("ItemDatabase: Item with ID '%s' already exists, overwriting" % item_data.id)
	
	_items[item_data.id] = item_data
	
	# Add to type dictionary
	if not item_data in _items_by_type[item_data.type]:
		_items_by_type[item_data.type].append(item_data)
	
	# Add to rarity dictionary
	if not item_data in _items_by_rarity[item_data.rarity]:
		_items_by_rarity[item_data.rarity].append(item_data)
	
	# Add to tag dictionaries
	for tag in item_data.tags:
		if not tag in _items_by_tag:
			_items_by_tag[tag] = []
		if not item_data in _items_by_tag[tag]:
			_items_by_tag[tag].append(item_data)
	
	item_registered.emit(item_data)
	return true

## Get an item by its ID
func get_item(item_id: String) -> ItemData:
	return _items.get(item_id, null)

## Check if an item exists in the database
func has_item(item_id: String) -> bool:
	return item_id in _items

## Get all items in the database
func get_all_items() -> Array:
	var items: Array = []
	for item in _items.values():
		items.append(item)
	return items

## Get items by type
func get_items_by_type(type: ItemData.ItemType) -> Array:
	var items: Array = []
	for item in _items_by_type.get(type, []):
		items.append(item)
	return items

## Get items by rarity
func get_items_by_rarity(rarity: ItemData.ItemRarity) -> Array:
	var items: Array = []
	for item in _items_by_rarity.get(rarity, []):
		items.append(item)
	return items

## Get items by tag
func get_items_by_tag(tag: String) -> Array:
	var items: Array = []
	for item in _items_by_tag.get(tag, []):
		items.append(item)
	return items

## Search items by name (case-insensitive partial match)
func search_items_by_name(search_term: String) -> Array:
	var results: Array = []
	var lower_search = search_term.to_lower()

	for item in _items.values():
		if lower_search in item.name.to_lower():
			results.append(item)

	return results

## Get items matching multiple criteria
func get_items_filtered(
	types: Array = [],
	rarities: Array = [],
	tags: Array = [],
	name_filter: String = ""
) -> Array:
	var results: Array = []
	
	for item in _items.values():
		var matches = true
		
		# Check type filter
		if not types.is_empty() and not item.type in types:
			matches = false
		
		# Check rarity filter
		if matches and not rarities.is_empty() and not item.rarity in rarities:
			matches = false
		
		# Check tag filter (item must have ALL specified tags)
		if matches and not tags.is_empty():
			for tag in tags:
				if not item.has_tag(tag):
					matches = false
					break
		
		# Check name filter
		if matches and not name_filter.is_empty():
			if not name_filter.to_lower() in item.name.to_lower():
				matches = false
		
		if matches:
			results.append(item)
	
	return results

## Load items from a JSON file
func load_from_json(file_path: String) -> bool:
	if not FileAccess.file_exists(file_path):
		push_error("ItemDatabase: JSON file not found: " + file_path)
		return false
	
	var file = FileAccess.open(file_path, FileAccess.READ)
	if not file:
		push_error("ItemDatabase: Could not open JSON file: " + file_path)
		return false
	
	var json_text = file.get_as_text()
	file.close()
	
	var json = JSON.new()
	var parse_result = json.parse(json_text)
	
	if parse_result != OK:
		push_error("ItemDatabase: Failed to parse JSON: " + json.get_error_message())
		return false
	
	var data = json.data
	if not data is Dictionary or not "items" in data:
		push_error("ItemDatabase: Invalid JSON format, expected 'items' array")
		return false
	
	var items_loaded = 0
	for item_dict in data["items"]:
		var item_data = _create_item_from_dict(item_dict)
		if item_data and register_item(item_data):
			items_loaded += 1
	
	is_loaded = true
	database_loaded.emit()
	print("ItemDatabase: Loaded %d items from %s" % [items_loaded, file_path])
	return true

## Create an ItemData from a dictionary
func _create_item_from_dict(data: Dictionary) -> ItemData:
	var item_data = ItemData.new()
	
	item_data.id = data.get("id", "")
	item_data.name = data.get("name", "")
	item_data.description = data.get("description", "")
	
	# Load icon if path is provided
	var icon_path = data.get("icon", "")
	if not icon_path.is_empty() and ResourceLoader.exists(icon_path):
		item_data.icon = load(icon_path)
	
	# Parse type
	var type_str = data.get("type", "MISC")
	if type_str in ItemData.ItemType:
		item_data.type = ItemData.ItemType[type_str]
	
	# Parse rarity
	var rarity_str = data.get("rarity", "COMMON")
	if rarity_str in ItemData.ItemRarity:
		item_data.rarity = ItemData.ItemRarity[rarity_str]
	
	item_data.max_stack_size = data.get("max_stack_size", 1)
	item_data.value = data.get("value", 0)
	item_data.can_drop = data.get("can_drop", true)
	item_data.can_trade = data.get("can_trade", true)
	item_data.can_sell = data.get("can_sell", true)
	item_data.tags = data.get("tags", [])
	item_data.custom_properties = data.get("custom_properties", {})
	
	return item_data

## Get database statistics
func get_stats() -> Dictionary:
	return {
		"total_items": _items.size(),
		"items_by_type": _get_type_counts(),
		"items_by_rarity": _get_rarity_counts(),
		"total_tags": _items_by_tag.size()
	}

func _get_type_counts() -> Dictionary:
	var counts = {}
	for type in ItemData.ItemType.values():
		counts[ItemData.ItemType.keys()[type]] = _items_by_type[type].size()
	return counts

func _get_rarity_counts() -> Dictionary:
	var counts = {}
	for rarity in ItemData.ItemRarity.values():
		counts[ItemData.ItemRarity.keys()[rarity]] = _items_by_rarity[rarity].size()
	return counts
