# AAA Inventory System for Godot 4.4.1

A production-ready, modular inventory system designed for 3D looter shooter survival games. This system provides enterprise-grade features with AAA performance optimization.

## Features

### Core Features
- **Modular Architecture**: Clean separation of concerns with extensible design
- **Advanced Item System**: Support for weapons, armor, consumables, materials, tools, and quest items
- **Flexible Containers**: Multiple inventory types with configurable restrictions
- **Drag & Drop**: Sophisticated drag and drop with visual feedback and validation
- **Rich UI Components**: Professional tooltips, filtering, sorting, and search
- **Event System**: Comprehensive event handling for external system integration
- **Performance Optimized**: Object pooling, efficient updates, and memory management

### Advanced Features
- **Item Rarity System**: 6 rarity levels with visual indicators
- **Durability System**: Item condition tracking with visual feedback
- **Stacking System**: Intelligent item stacking with configurable limits
- **Serialization**: Robust save/load with data validation and versioning
- **Filtering & Search**: Advanced filtering with multiple criteria and presets
- **Auto-sorting**: Configurable sorting algorithms
- **Item Modifications**: Support for item upgrades and modifications

## Quick Start

### 1. Installation
1. Copy the `addons/inventory_system` folder to your project
2. Enable the plugin in Project Settings > Plugins
3. The system will auto-initialize with default settings

### 2. Basic Usage

```gdscript
# Get the inventory manager (auto-loaded singleton)
var inventory = InventoryManager

# Create an item and add it to player inventory
inventory.give_item_to_player("sword_iron", 1)

# Create a custom container
var storage = InventoryContainer.new()
storage.container_name = "Storage Chest"
storage.max_slots = 20
inventory.register_container(storage, "storage_chest")

# Transfer items between containers
var item = inventory.create_item("potion_health", 5)
inventory.transfer_item(item, "player_inventory", "storage_chest")
```

### 3. UI Integration

```gdscript
# Create inventory UI
var inventory_ui = InventoryUI.new()
add_child(inventory_ui)

# Display player inventory
inventory_ui.display_container(inventory.player_inventory, "player_inventory")

# Handle inventory toggle
func _input(event):
    if event.is_action_pressed("inventory_toggle"):
        inventory_ui.toggle_inventory()
```

## Architecture Overview

### Core Components

#### ItemData (Resource)
Defines static properties of item types:
- Basic properties (name, description, icon, rarity)
- Gameplay properties (value, stack size, restrictions)
- Custom properties for game-specific data

#### ItemInstance (RefCounted)
Represents individual item instances:
- Dynamic properties (quantity, durability, modifications)
- Unique identification and timestamps
- Serialization support

#### InventoryContainer (Resource)
Manages collections of items:
- Configurable slot count and restrictions
- Type and rarity filtering
- Stacking and sorting capabilities

#### InventoryManager (Singleton)
Central system coordinator:
- Container management
- High-level operations
- Save/load functionality
- Event coordination

### UI Components

#### ItemSlot
Individual inventory slots with:
- Visual item representation
- Drag and drop support
- Mouse interaction handling
- Visual state feedback

#### InventoryUI
Main inventory interface:
- Grid-based layout
- Tab support for multiple containers
- Filtering and sorting controls
- Status information

#### ItemTooltip
Rich tooltip system:
- Detailed item information
- Item comparison support
- Dynamic positioning
- Customizable styling

## Configuration

### Item Database
Items are defined in JSON format:

```json
{
  "id": "sword_steel",
  "name": "Steel Sword",
  "description": "A well-crafted steel sword with superior balance.",
  "type": "WEAPON",
  "rarity": "UNCOMMON",
  "max_stack_size": 1,
  "value": 300,
  "tags": ["melee", "sword", "metal"],
  "custom_properties": {
    "damage": 35,
    "attack_speed": 1.3,
    "durability_max": 150
  }
}
```

### Container Configuration
```gdscript
var container = InventoryContainer.new()
container.container_type = InventoryContainer.ContainerType.STORAGE_CONTAINER
container.max_slots = 30
container.allowed_types = [ItemData.ItemType.MATERIAL, ItemData.ItemType.CONSUMABLE]
container.allow_stacking = true
container.allow_auto_sort = true
```

## Performance Features

### Object Pooling
Automatic pooling of UI components to reduce garbage collection:
```gdscript
# Automatically handled by PerformanceManager
var slot = PerformanceManager.get_pooled_item_slot()
# Use slot...
PerformanceManager.return_item_slot_to_pool(slot)
```

### Batch Operations
Efficient batch processing for multiple operations:
```gdscript
InventoryEvents.start_batch_processing()
# Perform multiple operations...
InventoryEvents.end_batch_processing()
```

### Memory Management
Automatic memory monitoring and optimization:
- Configurable memory thresholds
- Automatic pool trimming
- Performance metrics tracking

## Event System

### Event Types
- Item operations (add, remove, move, use)
- Equipment changes (equip, unequip)
- Container operations (create, destroy)
- System events (save, load, filter changes)

### Event Handling
```gdscript
# Register event listener
InventoryEvents.register_listener(
    InventoryEvents.EventType.ITEM_ADDED,
    _on_item_added
)

func _on_item_added(event: InventoryEvents.InventoryEvent):
    print("Item added: ", event.data.item.item_data.name)
```

## Integration Examples

### Equipment System Integration
```gdscript
# Listen for equipment events
InventoryEvents.register_listener(
    InventoryEvents.EventType.ITEM_EQUIPPED,
    _on_item_equipped
)

func _on_item_equipped(event):
    var item = event.data.item
    var slot = event.data.equipment_slot
    # Apply item stats to character
    character.apply_equipment_stats(item, slot)
```

### Crafting System Integration
```gdscript
# Consume materials and create result
func craft_item(recipe_id: String) -> bool:
    var recipe = get_recipe(recipe_id)
    
    # Check materials
    for material in recipe.materials:
        if inventory.get_total_item_count(material.id) < material.quantity:
            return false
    
    # Consume materials
    for material in recipe.materials:
        inventory.consume_item(material.id, material.quantity)
    
    # Create result
    var result = inventory.create_item(recipe.result_id, recipe.result_quantity)
    inventory.add_item_to_player(result)
    
    # Emit crafting event
    InventoryEvents.emit_item_crafted(result, recipe_id, recipe.materials)
    return true
```

## API Reference

### InventoryManager
- `give_item_to_player(item_id: String, quantity: int) -> bool`
- `create_item(item_id: String, quantity: int) -> ItemInstance`
- `transfer_item(item, from_container, to_container) -> bool`
- `save_inventory_data(file_path: String) -> bool`
- `load_inventory_data(file_path: String) -> bool`

### InventoryContainer
- `add_item(item: ItemInstance) -> bool`
- `remove_item_from_slot(slot_index: int) -> ItemInstance`
- `move_item(from_slot: int, to_slot: int) -> bool`
- `can_add_item(item: ItemInstance) -> bool`

### ItemFilter
- `set_name_filter(filter_text: String)`
- `add_type_filter(type: ItemData.ItemType)`
- `filter_items(items: Array[ItemInstance]) -> Array[ItemInstance]`
- `apply_preset(preset_name: String) -> bool`

## Best Practices

### Performance
1. Use object pooling for frequently created/destroyed UI elements
2. Batch operations when possible
3. Monitor performance metrics in development
4. Configure appropriate pool sizes for your use case

### Memory Management
1. Clear references to large objects when not needed
2. Use the event system instead of polling for changes
3. Configure garbage collection intervals appropriately
4. Monitor memory usage in production

### Architecture
1. Extend ItemData for game-specific item types
2. Use the event system for loose coupling
3. Implement custom validation rules for business logic
4. Use containers for different inventory types

## Troubleshooting

### Common Issues
1. **Items not appearing**: Check item database loading and item IDs
2. **Drag and drop not working**: Verify UI setup and event connections
3. **Performance issues**: Enable performance monitoring and check metrics
4. **Save/load problems**: Validate JSON format and file permissions

### Debug Tools
- Performance metrics: `PerformanceManager.get_performance_report()`
- Event history: `InventoryEvents.get_recent_events()`
- Container stats: `container.get_all_items().size()`

## License

This inventory system is provided as-is for educational and commercial use.

## Support

For issues and feature requests, please refer to the documentation or create detailed bug reports with reproduction steps.
