; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="Godot Inventory System"
config/description="Production-ready AAA inventory system for 3D looter shooter survival games"
config/version="1.0.0"
run/main_scene="res://demo/demo_scene.tscn"
config/features=PackedStringArray("4.4", "Forward Plus")
config/icon="res://icon.svg"

[autoload]

InventoryManager="*res://addons/inventory_system/core/inventory_manager.gd"
PerformanceManager="*res://addons/inventory_system/core/performance_manager.gd"

[dotnet]

project/assembly_name="Godot Inventory System"

[editor_plugins]

enabled=PackedStringArray("res://addons/inventory_system/plugin.cfg")

[input]

inventory_toggle={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":0,"physical_keycode":73,"key_label":0,"unicode":105,"location":0,"echo":false,"script":null)
]
}
