extends Control

## Demo scene showcasing the AAA Inventory System
## Demonstrates all major features and integration patterns

@onready var inventory_ui: InventoryUI
@onready var demo_controls: VBoxContainer
@onready var status_label: Label
@onready var performance_label: Label

## Demo data
var demo_items: Array[String] = [
	"sword_iron", "sword_steel", "bow_hunting",
	"armor_leather_chest", "armor_iron_chest",
	"potion_health", "potion_mana",
	"material_iron_ore", "material_iron_ingot", "material_wood",
	"tool_pickaxe_iron", "gem_ruby", "gem_emerald",
	"quest_ancient_key", "food_bread"
]

func _ready():
	_setup_ui()
	_setup_demo_controls()
	_setup_event_listeners()
	_populate_demo_inventory()
	
	# Show initial status
	_update_status("Demo loaded - Inventory system ready!")

## Setup the main UI
func _setup_ui():
	# Create main layout
	var main_vbox = VBoxContainer.new()
	main_vbox.anchors_preset = Control.PRESET_FULL_RECT
	add_child(main_vbox)
	
	# Title
	var title = Label.new()
	title.text = "AAA Inventory System Demo"
	title.add_theme_font_size_override("font_size", 24)
	title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	main_vbox.add_child(title)
	
	# Main content area
	var content_hbox = HBoxContainer.new()
	content_hbox.size_flags_vertical = Control.SIZE_EXPAND_FILL
	main_vbox.add_child(content_hbox)
	
	# Demo controls panel
	_create_demo_controls(content_hbox)
	
	# Inventory UI
	inventory_ui = InventoryUI.new()
	inventory_ui.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	content_hbox.add_child(inventory_ui)
	
	# Status bar
	var status_bar = HBoxContainer.new()
	main_vbox.add_child(status_bar)
	
	status_label = Label.new()
	status_label.text = "Ready"
	status_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	status_bar.add_child(status_label)
	
	performance_label = Label.new()
	performance_label.text = "Performance: OK"
	status_bar.add_child(performance_label)

## Create demo controls panel
func _create_demo_controls(parent: Control):
	var panel = Panel.new()
	panel.custom_minimum_size.x = 250
	parent.add_child(panel)
	
	demo_controls = VBoxContainer.new()
	demo_controls.anchors_preset = Control.PRESET_FULL_RECT
	demo_controls.add_theme_constant_override("separation", 8)
	panel.add_child(demo_controls)
	
	var controls_title = Label.new()
	controls_title.text = "Demo Controls"
	controls_title.add_theme_font_size_override("font_size", 18)
	demo_controls.add_child(controls_title)

## Setup demo control buttons
func _setup_demo_controls():
	# Item operations
	_add_section_title("Item Operations")
	_add_demo_button("Add Random Item", _add_random_item)
	_add_demo_button("Remove Random Item", _remove_random_item)
	_add_demo_button("Use Health Potion", _use_health_potion)
	
	# Container operations
	_add_section_title("Container Operations")
	_add_demo_button("Create Storage Chest", _create_storage_chest)
	_add_demo_button("Auto-Sort Inventory", _auto_sort_inventory)
	_add_demo_button("Compact Inventory", _compact_inventory)
	
	# Filter operations
	_add_section_title("Filter & Search")
	_add_demo_button("Show Weapons Only", _filter_weapons)
	_add_demo_button("Show Rare Items", _filter_rare_items)
	_add_demo_button("Clear Filters", _clear_filters)
	
	# System operations
	_add_section_title("System Operations")
	_add_demo_button("Save Inventory", _save_inventory)
	_add_demo_button("Load Inventory", _load_inventory)
	_add_demo_button("Show Performance", _show_performance)
	_add_demo_button("Reset Demo", _reset_demo)

## Add section title
func _add_section_title(title: String):
	var separator = HSeparator.new()
	demo_controls.add_child(separator)
	
	var label = Label.new()
	label.text = title
	label.add_theme_font_size_override("font_size", 14)
	label.add_theme_color_override("font_color", Color.YELLOW)
	demo_controls.add_child(label)

## Add demo button
func _add_demo_button(text: String, callback: Callable):
	var button = Button.new()
	button.text = text
	button.pressed.connect(callback)
	demo_controls.add_child(button)

## Setup event listeners
func _setup_event_listeners():
	var events = InventoryEvents.get_instance()

	# Listen to all inventory events for demo purposes
	events.register_global_listener(_on_inventory_event)

	# Listen to specific events
	events.register_listener(InventoryEvents.EventType.ITEM_ADDED, _on_item_added)
	events.register_listener(InventoryEvents.EventType.ITEM_REMOVED, _on_item_removed)
	events.register_listener(InventoryEvents.EventType.ITEM_USED, _on_item_used)

## Populate demo inventory with sample items
func _populate_demo_inventory():
	# Display player inventory
	inventory_ui.display_container(InventoryManager.player_inventory, "player_inventory")
	
	# Add some demo items
	for i in range(5):
		var item_id = demo_items[randi() % demo_items.size()]
		var quantity = 1 if _is_unique_item(item_id) else randi_range(1, 5)
		InventoryManager.give_item_to_player(item_id, quantity)

## Check if item should be unique
func _is_unique_item(item_id: String) -> bool:
	return item_id.begins_with("quest_") or item_id.begins_with("tool_") or item_id.begins_with("sword_") or item_id.begins_with("armor_")

## Demo button callbacks

func _add_random_item():
	var item_id = demo_items[randi() % demo_items.size()]
	var quantity = 1 if _is_unique_item(item_id) else randi_range(1, 3)
	
	if InventoryManager.give_item_to_player(item_id, quantity):
		_update_status("Added %d x %s" % [quantity, item_id])
	else:
		_update_status("Failed to add item - inventory full?")

func _remove_random_item():
	var items = InventoryManager.player_inventory.get_all_items()
	if items.is_empty():
		_update_status("No items to remove")
		return
	
	var random_item = items[randi() % items.size()]
	var result = InventoryOperations.remove_item_quantity(
		InventoryManager.player_inventory,
		random_item.item_data.id,
		1
	)
	
	if result.result == InventoryOperations.OperationResult.SUCCESS:
		_update_status("Removed 1 x %s" % random_item.item_data.name)
	else:
		_update_status("Failed to remove item")

func _use_health_potion():
	var result = InventoryOperations.remove_item_quantity(
		InventoryManager.player_inventory,
		"potion_health",
		1
	)
	
	if result.result == InventoryOperations.OperationResult.SUCCESS:
		_update_status("Used health potion - restored 50 HP!")
		InventoryEvents.get_instance().emit_item_used(result.removed_items[0], "player")
	else:
		_update_status("No health potions available")

func _create_storage_chest():
	var storage = InventoryContainer.new()
	storage.container_type = InventoryContainer.ContainerType.STORAGE_CONTAINER
	storage.container_name = "Storage Chest"
	storage.max_slots = 20
	
	var container_id = InventoryManager.register_container(storage, "storage_chest_%d" % Time.get_unix_time_from_system())
	inventory_ui.display_container(storage, container_id)
	
	_update_status("Created storage chest with 20 slots")

func _auto_sort_inventory():
	var result = InventoryOperations.auto_sort_container(InventoryManager.player_inventory)
	if result == InventoryOperations.OperationResult.SUCCESS:
		_update_status("Inventory sorted by type and rarity")
		inventory_ui._refresh_grid()
	else:
		_update_status("Failed to sort inventory")

func _compact_inventory():
	var result = InventoryOperations.compact_container(InventoryManager.player_inventory)
	if result == InventoryOperations.OperationResult.SUCCESS:
		_update_status("Inventory compacted - items merged")
		inventory_ui._refresh_grid()
	else:
		_update_status("Failed to compact inventory")

func _filter_weapons():
	inventory_ui.item_filter.clear_filters()
	inventory_ui.item_filter.add_type_filter(ItemData.ItemType.WEAPON)
	_update_status("Showing weapons only")

func _filter_rare_items():
	inventory_ui.item_filter.clear_filters()
	inventory_ui.item_filter.add_rarity_filter(ItemData.ItemRarity.RARE)
	inventory_ui.item_filter.add_rarity_filter(ItemData.ItemRarity.EPIC)
	inventory_ui.item_filter.add_rarity_filter(ItemData.ItemRarity.LEGENDARY)
	_update_status("Showing rare items only")

func _clear_filters():
	inventory_ui.item_filter.clear_filters()
	_update_status("All filters cleared")

func _save_inventory():
	if InventoryManager.save_inventory_data("user://demo_inventory.json"):
		_update_status("Inventory saved successfully")
	else:
		_update_status("Failed to save inventory")

func _load_inventory():
	if InventoryManager.load_inventory_data("user://demo_inventory.json"):
		_update_status("Inventory loaded successfully")
		inventory_ui._refresh_grid()
	else:
		_update_status("Failed to load inventory")

func _show_performance():
	var metrics = PerformanceManager.get_performance_report()
	var text = "Performance Report:\n"
	text += "Memory: %.1f MB\n" % metrics.memory_usage_mb
	text += "Frame Time: %.2f ms\n" % metrics.metrics.avg_frame_time
	text += "Pooled Objects: %d" % metrics.metrics.total_pooled_objects

	_update_status("Performance data logged to console")
	print(text)

func _reset_demo():
	# Clear inventory
	InventoryManager.player_inventory.clear()
	
	# Remove additional containers
	var containers_to_remove = []
	for container_id in InventoryManager.containers:
		if container_id != "player_inventory":
			containers_to_remove.append(container_id)
	
	for container_id in containers_to_remove:
		InventoryManager.unregister_container(container_id)
	
	# Repopulate with demo items
	_populate_demo_inventory()
	inventory_ui._refresh_grid()
	
	_update_status("Demo reset - inventory repopulated")

## Event handlers

func _on_inventory_event(event):
	# Update performance display periodically
	if randf() < 0.1:  # 10% chance to update performance
		_update_performance_display()

func _on_item_added(event):
	var item = event.data.get("item") as ItemInstance
	if item:
		print("Demo: Item added - %s x%d" % [item.item_data.name, item.quantity])

func _on_item_removed(event):
	var item = event.data.get("item") as ItemInstance
	if item:
		print("Demo: Item removed - %s x%d" % [item.item_data.name, item.quantity])

func _on_item_used(event):
	var item = event.data.get("item") as ItemInstance
	if item:
		print("Demo: Item used - %s" % item.item_data.name)

## Update status display
func _update_status(message: String):
	status_label.text = message
	print("Demo Status: " + message)

## Update performance display
func _update_performance_display():
	var metrics = PerformanceManager.get_performance_metrics()
	var status = "OK"

	if metrics.avg_frame_time > 16.67:
		status = "SLOW"
	elif metrics.avg_memory_usage > 100:
		status = "HIGH MEM"

	performance_label.text = "Performance: %s (%.1fms, %.1fMB)" % [
		status, metrics.avg_frame_time, metrics.avg_memory_usage
	]

## Handle input
func _input(event):
	if event.is_action_pressed("inventory_toggle"):
		inventory_ui.toggle_inventory()
		_update_status("Inventory toggled")
	elif event.is_action_pressed("ui_cancel"):
		get_tree().quit()
