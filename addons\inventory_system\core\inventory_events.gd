class_name InventoryEvents
extends RefCounted

## Comprehensive event system for inventory operations
## Provides hooks for external systems and detailed event tracking

## Event types
enum EventType {
	ITEM_ADDED,
	ITEM_REMOVED,
	ITEM_MOVED,
	ITEM_USED,
	ITEM_EQUIPPED,
	ITEM_UNEQUIPPED,
	ITEM_CRAFTED,
	ITEM_DESTROYED,
	CONT<PERSON>INER_CREATED,
	CONTAINER_DESTROYED,
	INVENTORY_LOADED,
	INVENTORY_SAVED,
	FILTER_CHANGED,
	SORT_CHANGED
}

## Event data structure
class InventoryEvent:
	var event_type: EventType
	var timestamp: float
	var event_id: String
	var data: Dictionary
	var source: String
	var processed: bool = false
	var cancelled: bool = false
	
	func _init(type: EventType, event_data: Dictionary = {}, event_source: String = ""):
		event_type = type
		data = event_data
		source = event_source
		timestamp = Time.get_unix_time_from_system()
		event_id = _generate_event_id()
	
	func _generate_event_id() -> String:
		return "evt_%d_%d" % [timestamp, randi()]
	
	func cancel():
		cancelled = true
	
	func is_cancelled() -> bool:
		return cancelled

## Event listeners
var event_listeners: Dictionary = {}
var global_listeners: Array[Callable] = []

## Event queue for batch processing
var event_queue: Array[InventoryEvent] = []
var batch_processing: bool = false

## Event history for debugging
var event_history: Array[InventoryEvent] = []
var max_history_size: int = 1000

## Statistics
var event_stats: Dictionary = {}

## Singleton instance
static var instance: InventoryEvents

static func get_instance() -> InventoryEvents:
	if not instance:
		instance = InventoryEvents.new()
	return instance

func _init():
	_initialize_stats()

## Initialize event statistics
func _initialize_stats():
	for event_type in EventType.values():
		event_stats[event_type] = 0

## Register event listener for specific event type
func register_listener(event_type: EventType, callback: Callable) -> bool:
	if not event_type in event_listeners:
		event_listeners[event_type] = []
	
	if callback in event_listeners[event_type]:
		push_warning("InventoryEvents: Listener already registered for event type %s" % EventType.keys()[event_type])
		return false
	
	event_listeners[event_type].append(callback)
	return true

## Unregister event listener
func unregister_listener(event_type: EventType, callback: Callable) -> bool:
	if not event_type in event_listeners:
		return false
	
	var index = event_listeners[event_type].find(callback)
	if index >= 0:
		event_listeners[event_type].remove_at(index)
		return true
	
	return false

## Register global listener (receives all events)
func register_global_listener(callback: Callable) -> bool:
	if callback in global_listeners:
		push_warning("InventoryEvents: Global listener already registered")
		return false
	
	global_listeners.append(callback)
	return true

## Unregister global listener
func unregister_global_listener(callback: Callable) -> bool:
	var index = global_listeners.find(callback)
	if index >= 0:
		global_listeners.remove_at(index)
		return true
	
	return false

## Emit event
func emit_event(event_type: EventType, data: Dictionary = {}, source: String = "") -> InventoryEvent:
	var event = InventoryEvent.new(event_type, data, source)
	
	if batch_processing:
		event_queue.append(event)
		return event
	
	_process_event(event)
	return event

## Process a single event
func _process_event(event: InventoryEvent):
	# Add to history
	_add_to_history(event)
	
	# Update statistics
	event_stats[event.event_type] += 1
	
	# Call global listeners first
	for listener in global_listeners:
		if event.is_cancelled():
			break
		_safe_call_listener(listener, event)
	
	# Call specific event listeners
	if event.event_type in event_listeners:
		for listener in event_listeners[event.event_type]:
			if event.is_cancelled():
				break
			_safe_call_listener(listener, event)
	
	event.processed = true

## Safely call listener with error handling
func _safe_call_listener(listener: Callable, event: InventoryEvent):
	try:
		listener.call(event)
	except:
		push_error("InventoryEvents: Error calling event listener: " + str(listener))

## Start batch processing
func start_batch_processing():
	batch_processing = true
	event_queue.clear()

## End batch processing and process queued events
func end_batch_processing():
	batch_processing = false
	
	for event in event_queue:
		_process_event(event)
	
	event_queue.clear()

## Add event to history
func _add_to_history(event: InventoryEvent):
	event_history.append(event)
	
	# Trim history if too large
	if event_history.size() > max_history_size:
		event_history.remove_at(0)

## Convenience methods for common events

## Item added event
func emit_item_added(item: ItemInstance, container_id: String, slot_index: int = -1):
	emit_event(EventType.ITEM_ADDED, {
		"item": item,
		"container_id": container_id,
		"slot_index": slot_index,
		"quantity": item.quantity if item else 0
	})

## Item removed event
func emit_item_removed(item: ItemInstance, container_id: String, slot_index: int = -1):
	emit_event(EventType.ITEM_REMOVED, {
		"item": item,
		"container_id": container_id,
		"slot_index": slot_index,
		"quantity": item.quantity if item else 0
	})

## Item moved event
func emit_item_moved(item: ItemInstance, from_container: String, from_slot: int, to_container: String, to_slot: int):
	emit_event(EventType.ITEM_MOVED, {
		"item": item,
		"from_container": from_container,
		"from_slot": from_slot,
		"to_container": to_container,
		"to_slot": to_slot
	})

## Item used event
func emit_item_used(item: ItemInstance, user_id: String = "", context: Dictionary = {}):
	emit_event(EventType.ITEM_USED, {
		"item": item,
		"user_id": user_id,
		"context": context
	})

## Item equipped event
func emit_item_equipped(item: ItemInstance, equipment_slot: String, character_id: String = ""):
	emit_event(EventType.ITEM_EQUIPPED, {
		"item": item,
		"equipment_slot": equipment_slot,
		"character_id": character_id
	})

## Item unequipped event
func emit_item_unequipped(item: ItemInstance, equipment_slot: String, character_id: String = ""):
	emit_event(EventType.ITEM_UNEQUIPPED, {
		"item": item,
		"equipment_slot": equipment_slot,
		"character_id": character_id
	})

## Item crafted event
func emit_item_crafted(crafted_item: ItemInstance, recipe_id: String, materials: Array[ItemInstance] = []):
	emit_event(EventType.ITEM_CRAFTED, {
		"crafted_item": crafted_item,
		"recipe_id": recipe_id,
		"materials": materials
	})

## Item destroyed event
func emit_item_destroyed(item: ItemInstance, reason: String = ""):
	emit_event(EventType.ITEM_DESTROYED, {
		"item": item,
		"reason": reason
	})

## Container created event
func emit_container_created(container: InventoryContainer, container_id: String):
	emit_event(EventType.CONTAINER_CREATED, {
		"container": container,
		"container_id": container_id,
		"container_type": container.container_type
	})

## Container destroyed event
func emit_container_destroyed(container_id: String, container_type: InventoryContainer.ContainerType):
	emit_event(EventType.CONTAINER_DESTROYED, {
		"container_id": container_id,
		"container_type": container_type
	})

## Inventory loaded event
func emit_inventory_loaded(file_path: String, containers_loaded: int):
	emit_event(EventType.INVENTORY_LOADED, {
		"file_path": file_path,
		"containers_loaded": containers_loaded,
		"load_time": Time.get_unix_time_from_system()
	})

## Inventory saved event
func emit_inventory_saved(file_path: String, containers_saved: int):
	emit_event(EventType.INVENTORY_SAVED, {
		"file_path": file_path,
		"containers_saved": containers_saved,
		"save_time": Time.get_unix_time_from_system()
	})

## Filter changed event
func emit_filter_changed(filter_criteria: Dictionary):
	emit_event(EventType.FILTER_CHANGED, {
		"filter_criteria": filter_criteria
	})

## Sort changed event
func emit_sort_changed(sort_criteria: String, ascending: bool):
	emit_event(EventType.SORT_CHANGED, {
		"sort_criteria": sort_criteria,
		"ascending": ascending
	})

## Query methods

## Get events by type
func get_events_by_type(event_type: EventType) -> Array[InventoryEvent]:
	var filtered_events: Array[InventoryEvent] = []
	for event in event_history:
		if event.event_type == event_type:
			filtered_events.append(event)
	return filtered_events

## Get events in time range
func get_events_in_range(start_time: float, end_time: float) -> Array[InventoryEvent]:
	var filtered_events: Array[InventoryEvent] = []
	for event in event_history:
		if event.timestamp >= start_time and event.timestamp <= end_time:
			filtered_events.append(event)
	return filtered_events

## Get recent events
func get_recent_events(count: int = 10) -> Array[InventoryEvent]:
	var recent_events: Array[InventoryEvent] = []
	var start_index = maxi(0, event_history.size() - count)
	
	for i in range(start_index, event_history.size()):
		recent_events.append(event_history[i])
	
	return recent_events

## Get event statistics
func get_event_statistics() -> Dictionary:
	return event_stats.duplicate()

## Get total events processed
func get_total_events() -> int:
	var total = 0
	for count in event_stats.values():
		total += count
	return total

## Clear event history
func clear_history():
	event_history.clear()

## Clear event statistics
func clear_statistics():
	_initialize_stats()

## Set maximum history size
func set_max_history_size(size: int):
	max_history_size = size
	
	# Trim current history if needed
	while event_history.size() > max_history_size:
		event_history.remove_at(0)

## Export event history to dictionary
func export_history() -> Dictionary:
	var exported_events = []
	
	for event in event_history:
		exported_events.append({
			"event_type": EventType.keys()[event.event_type],
			"timestamp": event.timestamp,
			"event_id": event.event_id,
			"data": event.data,
			"source": event.source,
			"processed": event.processed,
			"cancelled": event.cancelled
		})
	
	return {
		"events": exported_events,
		"statistics": event_stats,
		"export_time": Time.get_unix_time_from_system()
	}

## Import event history from dictionary
func import_history(data: Dictionary):
	if not "events" in data:
		push_error("InventoryEvents: Invalid import data")
		return
	
	event_history.clear()
	
	for event_data in data["events"]:
		var event = InventoryEvent.new(
			EventType[event_data.get("event_type", "ITEM_ADDED")],
			event_data.get("data", {}),
			event_data.get("source", "")
		)
		event.timestamp = event_data.get("timestamp", 0.0)
		event.event_id = event_data.get("event_id", "")
		event.processed = event_data.get("processed", false)
		event.cancelled = event_data.get("cancelled", false)
		
		event_history.append(event)
	
	if "statistics" in data:
		event_stats = data["statistics"]
