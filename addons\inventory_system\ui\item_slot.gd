class_name ItemSlot
extends Control

## Individual inventory slot that can hold and display an item
## Handles mouse interactions, visual feedback, and drag/drop operations

signal item_clicked(slot: ItemSlot, button: int)
signal item_double_clicked(slot: ItemSlot)
signal item_right_clicked(slot: ItemSlot)
signal drag_started(slot: ItemSlot)
signal drag_ended(slot: ItemSlot)
signal drop_received(slot: ItemSlot, data: Variant)

## The item instance currently in this slot
var item: ItemInstance

## The container this slot belongs to
var container: InventoryContainer

## The slot index in the container
var slot_index: int = -1

## Visual components
@onready var background: NinePatchRect
@onready var item_icon: TextureRect
@onready var quantity_label: Label
@onready var durability_bar: ProgressBar
@onready var rarity_border: NinePatchRect
@onready var selection_overlay: ColorRect

## Slot configuration
@export var slot_size: Vector2 = Vector2(64, 64)
@export var show_quantity: bool = true
@export var show_durability: bool = true
@export var show_rarity_border: bool = true
@export var allow_drag: bool = true
@export var allow_drop: bool = true

## Visual states
var is_selected: bool = false
var is_highlighted: bool = false
var is_drag_preview: bool = false

## Drag and drop data
var drag_data: Dictionary = {}

func _ready():
	_setup_ui()
	_connect_signals()
	update_display()

## Setup the UI components
func _setup_ui():
	custom_minimum_size = slot_size
	size = slot_size
	
	# Background
	background = NinePatchRect.new()
	background.name = "Background"
	background.anchors_preset = Control.PRESET_FULL_RECT
	add_child(background)
	
	# Rarity border
	if show_rarity_border:
		rarity_border = NinePatchRect.new()
		rarity_border.name = "RarityBorder"
		rarity_border.anchors_preset = Control.PRESET_FULL_RECT
		rarity_border.visible = false
		add_child(rarity_border)
	
	# Item icon
	item_icon = TextureRect.new()
	item_icon.name = "ItemIcon"
	item_icon.anchors_preset = Control.PRESET_FULL_RECT
	item_icon.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
	item_icon.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
	add_child(item_icon)
	
	# Quantity label
	if show_quantity:
		quantity_label = Label.new()
		quantity_label.name = "QuantityLabel"
		quantity_label.anchor_left = 1.0
		quantity_label.anchor_top = 1.0
		quantity_label.anchor_right = 1.0
		quantity_label.anchor_bottom = 1.0
		quantity_label.offset_left = -20
		quantity_label.offset_top = -16
		quantity_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
		quantity_label.vertical_alignment = VERTICAL_ALIGNMENT_BOTTOM
		quantity_label.add_theme_color_override("font_color", Color.WHITE)
		quantity_label.add_theme_color_override("font_shadow_color", Color.BLACK)
		quantity_label.add_theme_constant_override("shadow_offset_x", 1)
		quantity_label.add_theme_constant_override("shadow_offset_y", 1)
		add_child(quantity_label)
	
	# Durability bar
	if show_durability:
		durability_bar = ProgressBar.new()
		durability_bar.name = "DurabilityBar"
		durability_bar.anchor_left = 0.0
		durability_bar.anchor_top = 1.0
		durability_bar.anchor_right = 1.0
		durability_bar.anchor_bottom = 1.0
		durability_bar.offset_top = -4
		durability_bar.show_percentage = false
		durability_bar.visible = false
		add_child(durability_bar)
	
	# Selection overlay
	selection_overlay = ColorRect.new()
	selection_overlay.name = "SelectionOverlay"
	selection_overlay.anchors_preset = Control.PRESET_FULL_RECT
	selection_overlay.color = Color(1, 1, 1, 0.3)
	selection_overlay.visible = false
	add_child(selection_overlay)

## Connect signals
func _connect_signals():
	gui_input.connect(_on_gui_input)
	mouse_entered.connect(_on_mouse_entered)
	mouse_exited.connect(_on_mouse_exited)

## Set the item for this slot
func set_item(new_item: ItemInstance):
	if item:
		_disconnect_item_signals()
	
	item = new_item
	
	if item:
		_connect_item_signals()
	
	update_display()

## Update the visual display
func update_display():
	if not is_inside_tree():
		return
	
	if item and item.item_data:
		_show_item()
	else:
		_show_empty()

## Show item in slot
func _show_item():
	# Set icon
	if item_icon and item.item_data.icon:
		item_icon.texture = item.item_data.icon
		item_icon.visible = true
	
	# Set quantity
	if quantity_label:
		if item.quantity > 1:
			quantity_label.text = str(item.quantity)
			quantity_label.visible = true
		else:
			quantity_label.visible = false
	
	# Set durability bar
	if durability_bar:
		if item.item_data.max_stack_size == 1 and item.durability < 1.0:
			durability_bar.value = item.durability * 100
			durability_bar.visible = true
			
			# Color based on durability
			var bar_color = Color.GREEN
			if item.durability < 0.3:
				bar_color = Color.RED
			elif item.durability < 0.6:
				bar_color = Color.YELLOW
			
			durability_bar.add_theme_color_override("fill", bar_color)
		else:
			durability_bar.visible = false
	
	# Set rarity border
	if rarity_border:
		rarity_border.visible = true
		var rarity_color = item.item_data.get_rarity_color()
		rarity_border.modulate = rarity_color

## Show empty slot
func _show_empty():
	if item_icon:
		item_icon.visible = false
	
	if quantity_label:
		quantity_label.visible = false
	
	if durability_bar:
		durability_bar.visible = false
	
	if rarity_border:
		rarity_border.visible = false

## Connect item signals
func _connect_item_signals():
	if item:
		item.quantity_changed.connect(_on_item_quantity_changed)
		item.durability_changed.connect(_on_item_durability_changed)

## Disconnect item signals
func _disconnect_item_signals():
	if item:
		if item.quantity_changed.is_connected(_on_item_quantity_changed):
			item.quantity_changed.disconnect(_on_item_quantity_changed)
		if item.durability_changed.is_connected(_on_item_durability_changed):
			item.durability_changed.disconnect(_on_item_durability_changed)

## Handle GUI input
func _on_gui_input(event: InputEvent):
	if event is InputEventMouseButton:
		var mouse_event = event as InputEventMouseButton
		
		if mouse_event.pressed:
			match mouse_event.button_index:
				MOUSE_BUTTON_LEFT:
					if mouse_event.double_click:
						item_double_clicked.emit(self)
					else:
						item_clicked.emit(self, MOUSE_BUTTON_LEFT)
				MOUSE_BUTTON_RIGHT:
					item_right_clicked.emit(self)
					item_clicked.emit(self, MOUSE_BUTTON_RIGHT)

## Handle mouse enter
func _on_mouse_entered():
	set_highlighted(true)

## Handle mouse exit
func _on_mouse_exited():
	set_highlighted(false)

## Set selection state
func set_selected(selected: bool):
	is_selected = selected
	if selection_overlay:
		selection_overlay.visible = is_selected

## Set highlight state
func set_highlighted(highlighted: bool):
	is_highlighted = highlighted
	if background:
		background.modulate = Color(1.2, 1.2, 1.2) if highlighted else Color.WHITE

## Handle item quantity change
func _on_item_quantity_changed(old_quantity: int, new_quantity: int):
	update_display()

## Handle item durability change
func _on_item_durability_changed(old_durability: float, new_durability: float):
	update_display()

## Get drag data
func _get_drag_data(position: Vector2) -> Variant:
	if not allow_drag or not item:
		return null
	
	drag_started.emit(self)
	
	# Create drag preview
	var preview = _create_drag_preview()
	set_drag_preview(preview)
	
	# Return drag data
	drag_data = {
		"type": "inventory_item",
		"item": item,
		"source_slot": self,
		"source_container": container,
		"source_index": slot_index
	}
	
	return drag_data

## Create drag preview
func _create_drag_preview() -> Control:
	var preview = ItemSlot.new()
	preview.is_drag_preview = true
	preview.slot_size = slot_size * 0.8
	preview.set_item(item)
	preview.modulate.a = 0.8
	return preview

## Handle drop data
func _can_drop_data(position: Vector2, data: Variant) -> bool:
	if not allow_drop:
		return false
	
	if not data is Dictionary:
		return false
	
	var drop_data = data as Dictionary
	if drop_data.get("type", "") != "inventory_item":
		return false
	
	var dropped_item = drop_data.get("item", null) as ItemInstance
	if not dropped_item:
		return false
	
	# Check if container allows this item
	if container and not container.can_add_item_to_slot(dropped_item, slot_index):
		return false
	
	return true

## Handle drop
func _drop_data(position: Vector2, data: Variant):
	if not _can_drop_data(position, data):
		return
	
	drop_received.emit(self, data)
	drag_ended.emit(self)

## Get tooltip text
func get_tooltip_text() -> String:
	if not item or not item.item_data:
		return ""
	
	return item.item_data.get_tooltip_text()

## Check if slot is empty
func is_empty() -> bool:
	return item == null

## Check if slot has item
func has_item() -> bool:
	return item != null

## Clear the slot
func clear():
	set_item(null)

## Get item count
func get_item_count() -> int:
	return item.quantity if item else 0

## Check if item can stack with slot content
func can_stack_with(other_item: ItemInstance) -> bool:
	if not item:
		return true
	
	return item.can_stack_with(other_item)

## Get available stack space
func get_stack_space() -> int:
	if not item:
		return 0
	
	return item.get_stack_space()

## Set slot configuration
func configure_slot(config: Dictionary):
	if "slot_size" in config:
		slot_size = config.slot_size
		custom_minimum_size = slot_size
		size = slot_size
	
	if "show_quantity" in config:
		show_quantity = config.show_quantity
		if quantity_label:
			quantity_label.visible = show_quantity and item and item.quantity > 1
	
	if "show_durability" in config:
		show_durability = config.show_durability
		if durability_bar:
			durability_bar.visible = show_durability and item and item.durability < 1.0
	
	if "show_rarity_border" in config:
		show_rarity_border = config.show_rarity_border
		if rarity_border:
			rarity_border.visible = show_rarity_border and item
	
	if "allow_drag" in config:
		allow_drag = config.allow_drag
	
	if "allow_drop" in config:
		allow_drop = config.allow_drop
