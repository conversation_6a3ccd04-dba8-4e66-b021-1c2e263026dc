class_name Item<PERSON><PERSON><PERSON>
extends RefCounted

## Advanced item filtering and categorization system
## Provides flexible filtering with multiple criteria and custom predicates

signal filter_changed()

## Filter criteria structure
class FilterCriteria:
	var name_filter: String = ""
	var type_filters: Array[ItemData.ItemType] = []
	var rarity_filters: Array[ItemData.ItemRarity] = []
	var tag_filters: Array[String] = []
	var value_range: Vector2 = Vector2(-1, -1)  # -1 means no limit
	var durability_range: Vector2 = Vector2(-1, -1)
	var custom_predicates: Array[Callable] = []
	var sort_criteria: String = "name"
	var sort_ascending: bool = true
	var include_broken_items: bool = true
	var include_empty_stacks: bool = false
	
	func duplicate() -> FilterCriteria:
		var new_criteria = FilterCriteria.new()
		new_criteria.name_filter = name_filter
		new_criteria.type_filters = type_filters.duplicate()
		new_criteria.rarity_filters = rarity_filters.duplicate()
		new_criteria.tag_filters = tag_filters.duplicate()
		new_criteria.value_range = value_range
		new_criteria.durability_range = durability_range
		new_criteria.custom_predicates = custom_predicates.duplicate()
		new_criteria.sort_criteria = sort_criteria
		new_criteria.sort_ascending = sort_ascending
		new_criteria.include_broken_items = include_broken_items
		new_criteria.include_empty_stacks = include_empty_stacks
		return new_criteria

## Current filter criteria
var criteria: FilterCriteria

## Predefined filter presets
var presets: Dictionary = {}

func _init():
	criteria = FilterCriteria.new()
	_setup_default_presets()

## Setup default filter presets
func _setup_default_presets():
	# Weapons only
	var weapons_preset = FilterCriteria.new()
	weapons_preset.type_filters = [ItemData.ItemType.WEAPON]
	presets["weapons"] = weapons_preset
	
	# Armor only
	var armor_preset = FilterCriteria.new()
	armor_preset.type_filters = [ItemData.ItemType.ARMOR]
	presets["armor"] = armor_preset
	
	# Consumables only
	var consumables_preset = FilterCriteria.new()
	consumables_preset.type_filters = [ItemData.ItemType.CONSUMABLE]
	presets["consumables"] = consumables_preset
	
	# Materials only
	var materials_preset = FilterCriteria.new()
	materials_preset.type_filters = [ItemData.ItemType.MATERIAL]
	presets["materials"] = materials_preset
	
	# Rare and above
	var rare_preset = FilterCriteria.new()
	rare_preset.rarity_filters = [
		ItemData.ItemRarity.RARE,
		ItemData.ItemRarity.EPIC,
		ItemData.ItemRarity.LEGENDARY,
		ItemData.ItemRarity.MYTHIC
	]
	presets["rare_items"] = rare_preset
	
	# Valuable items
	var valuable_preset = FilterCriteria.new()
	valuable_preset.value_range = Vector2(100, -1)
	presets["valuable"] = valuable_preset
	
	# Broken items
	var broken_preset = FilterCriteria.new()
	broken_preset.durability_range = Vector2(0, 0.1)
	presets["broken"] = broken_preset

## Apply a preset filter
func apply_preset(preset_name: String) -> bool:
	if not preset_name in presets:
		push_warning("ItemFilter: Preset '%s' not found" % preset_name)
		return false
	
	criteria = presets[preset_name].duplicate()
	filter_changed.emit()
	return true

## Set name filter
func set_name_filter(filter_text: String):
	criteria.name_filter = filter_text.strip_edges()
	filter_changed.emit()

## Add type filter
func add_type_filter(type: ItemData.ItemType):
	if not type in criteria.type_filters:
		criteria.type_filters.append(type)
		filter_changed.emit()

## Remove type filter
func remove_type_filter(type: ItemData.ItemType):
	var index = criteria.type_filters.find(type)
	if index >= 0:
		criteria.type_filters.remove_at(index)
		filter_changed.emit()

## Toggle type filter
func toggle_type_filter(type: ItemData.ItemType):
	if type in criteria.type_filters:
		remove_type_filter(type)
	else:
		add_type_filter(type)

## Add rarity filter
func add_rarity_filter(rarity: ItemData.ItemRarity):
	if not rarity in criteria.rarity_filters:
		criteria.rarity_filters.append(rarity)
		filter_changed.emit()

## Remove rarity filter
func remove_rarity_filter(rarity: ItemData.ItemRarity):
	var index = criteria.rarity_filters.find(rarity)
	if index >= 0:
		criteria.rarity_filters.remove_at(index)
		filter_changed.emit()

## Toggle rarity filter
func toggle_rarity_filter(rarity: ItemData.ItemRarity):
	if rarity in criteria.rarity_filters:
		remove_rarity_filter(rarity)
	else:
		add_rarity_filter(rarity)

## Add tag filter
func add_tag_filter(tag: String):
	if not tag in criteria.tag_filters:
		criteria.tag_filters.append(tag)
		filter_changed.emit()

## Remove tag filter
func remove_tag_filter(tag: String):
	var index = criteria.tag_filters.find(tag)
	if index >= 0:
		criteria.tag_filters.remove_at(index)
		filter_changed.emit()

## Set value range filter
func set_value_range(min_value: int = -1, max_value: int = -1):
	criteria.value_range = Vector2(min_value, max_value)
	filter_changed.emit()

## Set durability range filter
func set_durability_range(min_durability: float = -1, max_durability: float = -1):
	criteria.durability_range = Vector2(min_durability, max_durability)
	filter_changed.emit()

## Add custom predicate filter
func add_custom_predicate(predicate: Callable):
	if not predicate in criteria.custom_predicates:
		criteria.custom_predicates.append(predicate)
		filter_changed.emit()

## Remove custom predicate filter
func remove_custom_predicate(predicate: Callable):
	var index = criteria.custom_predicates.find(predicate)
	if index >= 0:
		criteria.custom_predicates.remove_at(index)
		filter_changed.emit()

## Set sort criteria
func set_sort_criteria(sort_by: String, ascending: bool = true):
	criteria.sort_criteria = sort_by
	criteria.sort_ascending = ascending
	filter_changed.emit()

## Clear all filters
func clear_filters():
	criteria = FilterCriteria.new()
	filter_changed.emit()

## Check if an item passes the current filter
func passes_filter(item: ItemInstance) -> bool:
	if not item or not item.item_data:
		return false
	
	# Check empty stacks
	if not criteria.include_empty_stacks and item.is_empty():
		return false
	
	# Check broken items
	if not criteria.include_broken_items and item.is_broken():
		return false
	
	# Check name filter
	if not criteria.name_filter.is_empty():
		var name_lower = item.item_data.name.to_lower()
		var filter_lower = criteria.name_filter.to_lower()
		if not filter_lower in name_lower:
			return false
	
	# Check type filters
	if not criteria.type_filters.is_empty():
		if not item.item_data.type in criteria.type_filters:
			return false
	
	# Check rarity filters
	if not criteria.rarity_filters.is_empty():
		if not item.item_data.rarity in criteria.rarity_filters:
			return false
	
	# Check tag filters (item must have ALL specified tags)
	for tag in criteria.tag_filters:
		if not item.item_data.has_tag(tag):
			return false
	
	# Check value range
	if criteria.value_range.x >= 0 or criteria.value_range.y >= 0:
		var item_value = item.get_total_value()
		if criteria.value_range.x >= 0 and item_value < criteria.value_range.x:
			return false
		if criteria.value_range.y >= 0 and item_value > criteria.value_range.y:
			return false
	
	# Check durability range
	if criteria.durability_range.x >= 0 or criteria.durability_range.y >= 0:
		if criteria.durability_range.x >= 0 and item.durability < criteria.durability_range.x:
			return false
		if criteria.durability_range.y >= 0 and item.durability > criteria.durability_range.y:
			return false
	
	# Check custom predicates
	for predicate in criteria.custom_predicates:
		if not predicate.call(item):
			return false
	
	return true

## Filter a list of items
func filter_items(items: Array[ItemInstance]) -> Array[ItemInstance]:
	var filtered_items: Array[ItemInstance] = []
	
	for item in items:
		if passes_filter(item):
			filtered_items.append(item)
	
	# Sort the filtered items
	if not filtered_items.is_empty():
		filtered_items.sort_custom(_get_sort_function())
	
	return filtered_items

## Filter items from a container
func filter_container(container: InventoryContainer) -> Array[ItemInstance]:
	return filter_items(container.get_all_items())

## Get sort function based on criteria
func _get_sort_function() -> Callable:
	match criteria.sort_criteria:
		"name":
			return _sort_by_name if criteria.sort_ascending else _sort_by_name_desc
		"type":
			return _sort_by_type if criteria.sort_ascending else _sort_by_type_desc
		"rarity":
			return _sort_by_rarity if criteria.sort_ascending else _sort_by_rarity_desc
		"value":
			return _sort_by_value if criteria.sort_ascending else _sort_by_value_desc
		"quantity":
			return _sort_by_quantity if criteria.sort_ascending else _sort_by_quantity_desc
		"durability":
			return _sort_by_durability if criteria.sort_ascending else _sort_by_durability_desc
		_:
			return _sort_by_name

## Sorting functions
func _sort_by_name(a: ItemInstance, b: ItemInstance) -> bool:
	return a.item_data.name < b.item_data.name

func _sort_by_name_desc(a: ItemInstance, b: ItemInstance) -> bool:
	return a.item_data.name > b.item_data.name

func _sort_by_type(a: ItemInstance, b: ItemInstance) -> bool:
	return a.item_data.type < b.item_data.type

func _sort_by_type_desc(a: ItemInstance, b: ItemInstance) -> bool:
	return a.item_data.type > b.item_data.type

func _sort_by_rarity(a: ItemInstance, b: ItemInstance) -> bool:
	return a.item_data.rarity < b.item_data.rarity

func _sort_by_rarity_desc(a: ItemInstance, b: ItemInstance) -> bool:
	return a.item_data.rarity > b.item_data.rarity

func _sort_by_value(a: ItemInstance, b: ItemInstance) -> bool:
	return a.get_total_value() < b.get_total_value()

func _sort_by_value_desc(a: ItemInstance, b: ItemInstance) -> bool:
	return a.get_total_value() > b.get_total_value()

func _sort_by_quantity(a: ItemInstance, b: ItemInstance) -> bool:
	return a.quantity < b.quantity

func _sort_by_quantity_desc(a: ItemInstance, b: ItemInstance) -> bool:
	return a.quantity > b.quantity

func _sort_by_durability(a: ItemInstance, b: ItemInstance) -> bool:
	return a.durability < b.durability

func _sort_by_durability_desc(a: ItemInstance, b: ItemInstance) -> bool:
	return a.durability > b.durability

## Get filter summary
func get_filter_summary() -> Dictionary:
	return {
		"name_filter": criteria.name_filter,
		"type_count": criteria.type_filters.size(),
		"rarity_count": criteria.rarity_filters.size(),
		"tag_count": criteria.tag_filters.size(),
		"has_value_range": criteria.value_range.x >= 0 or criteria.value_range.y >= 0,
		"has_durability_range": criteria.durability_range.x >= 0 or criteria.durability_range.y >= 0,
		"custom_predicates_count": criteria.custom_predicates.size(),
		"sort_criteria": criteria.sort_criteria,
		"sort_ascending": criteria.sort_ascending
	}

## Check if any filters are active
func has_active_filters() -> bool:
	return (
		not criteria.name_filter.is_empty() or
		not criteria.type_filters.is_empty() or
		not criteria.rarity_filters.is_empty() or
		not criteria.tag_filters.is_empty() or
		criteria.value_range.x >= 0 or criteria.value_range.y >= 0 or
		criteria.durability_range.x >= 0 or criteria.durability_range.y >= 0 or
		not criteria.custom_predicates.is_empty()
	)

## Save current filter as preset
func save_as_preset(preset_name: String):
	presets[preset_name] = criteria.duplicate()

## Remove a preset
func remove_preset(preset_name: String) -> bool:
	if preset_name in presets:
		presets.erase(preset_name)
		return true
	return false

## Get all preset names
func get_preset_names() -> Array[String]:
	var names: Array[String] = []
	for name in presets.keys():
		names.append(name)
	return names
