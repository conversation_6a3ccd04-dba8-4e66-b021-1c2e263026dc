class_name ItemInstance
extends RefCounted

## Represents a specific instance of an item with unique properties
## This class handles individual item instances with quantity, durability, modifications, etc.

signal quantity_changed(old_quantity: int, new_quantity: int)
signal durability_changed(old_durability: float, new_durability: float)

## Reference to the item definition
var item_data: ItemData

## Current quantity of this item stack
var quantity: int = 1

## Current durability (0.0 to 1.0, where 1.0 is full durability)
var durability: float = 1.0

## Unique identifier for this specific item instance
var unique_id: String

## Custom modifications applied to this item
var modifications: Dictionary = {}

## Custom properties for this specific instance
var instance_properties: Dictionary = {}

## Timestamp when this item was created
var created_timestamp: float

## Timestamp when this item was last modified
var modified_timestamp: float

func _init(p_item_data: ItemData = null, p_quantity: int = 1):
	if p_item_data:
		item_data = p_item_data
	quantity = p_quantity
	unique_id = _generate_unique_id()
	created_timestamp = Time.get_unix_time_from_system()
	modified_timestamp = created_timestamp

## Generate a unique identifier for this item instance
func _generate_unique_id() -> String:
	var time_str = str(Time.get_unix_time_from_system())
	var random_str = str(randi())
	return "%s_%s_%s" % [item_data.id if item_data else "unknown", time_str, random_str]

## Set the quantity and emit signal if changed
func set_quantity(new_quantity: int) -> void:
	if new_quantity < 0:
		push_error("ItemInstance: Quantity cannot be negative")
		return
	
	if item_data and new_quantity > item_data.max_stack_size:
		push_error("ItemInstance: Quantity exceeds max stack size")
		return
	
	var old_quantity = quantity
	quantity = new_quantity
	modified_timestamp = Time.get_unix_time_from_system()
	
	if old_quantity != new_quantity:
		quantity_changed.emit(old_quantity, new_quantity)

## Set the durability and emit signal if changed
func set_durability(new_durability: float) -> void:
	new_durability = clampf(new_durability, 0.0, 1.0)
	var old_durability = durability
	durability = new_durability
	modified_timestamp = Time.get_unix_time_from_system()
	
	if not is_equal_approx(old_durability, new_durability):
		durability_changed.emit(old_durability, new_durability)

## Check if this item can stack with another item instance
func can_stack_with(other: ItemInstance) -> bool:
	if not other or not item_data or not other.item_data:
		return false
	
	# Must be the same item type
	if item_data.id != other.item_data.id:
		return false
	
	# Must have same durability for stackable items
	if not is_equal_approx(durability, other.durability):
		return false
	
	# Must have same modifications
	if modifications != other.modifications:
		return false
	
	return true

## Get the maximum quantity that can be added to this stack
func get_stack_space() -> int:
	if not item_data:
		return 0
	return item_data.max_stack_size - quantity

## Add quantity to this stack, returns the amount that couldn't be added
func add_quantity(amount: int) -> int:
	if amount <= 0:
		return amount
	
	var space = get_stack_space()
	var can_add = mini(amount, space)
	set_quantity(quantity + can_add)
	return amount - can_add

## Remove quantity from this stack, returns the amount actually removed
func remove_quantity(amount: int) -> int:
	if amount <= 0:
		return 0
	
	var can_remove = mini(amount, quantity)
	set_quantity(quantity - can_remove)
	return can_remove

## Split this stack into two, returns the new stack with the specified quantity
func split(split_quantity: int) -> ItemInstance:
	if split_quantity <= 0 or split_quantity >= quantity:
		return null
	
	var new_instance = duplicate()
	new_instance.set_quantity(split_quantity)
	set_quantity(quantity - split_quantity)
	
	return new_instance

## Create a duplicate of this item instance
func duplicate() -> ItemInstance:
	var new_instance = ItemInstance.new(item_data, quantity)
	new_instance.durability = durability
	new_instance.modifications = modifications.duplicate(true)
	new_instance.instance_properties = instance_properties.duplicate(true)
	return new_instance

## Check if this item is broken (durability <= 0)
func is_broken() -> bool:
	return durability <= 0.0

## Check if this item stack is empty
func is_empty() -> bool:
	return quantity <= 0

## Get the total value of this item stack
func get_total_value() -> int:
	if not item_data:
		return 0
	var base_value = item_data.value * quantity
	var durability_modifier = durability if not is_broken() else 0.1
	return int(base_value * durability_modifier)

## Serialize this item instance to a dictionary
func to_dict() -> Dictionary:
	return {
		"item_id": item_data.id if item_data else "",
		"quantity": quantity,
		"durability": durability,
		"unique_id": unique_id,
		"modifications": modifications,
		"instance_properties": instance_properties,
		"created_timestamp": created_timestamp,
		"modified_timestamp": modified_timestamp
	}

## Create an item instance from a dictionary
static func from_dict(data: Dictionary, item_database) -> ItemInstance:
	var item_data_ref = item_database.get_item(data.get("item_id", ""))
	if not item_data_ref:
		push_error("ItemInstance: Could not find item data for ID: " + str(data.get("item_id", "")))
		return null
	
	var instance = ItemInstance.new(item_data_ref, data.get("quantity", 1))
	instance.durability = data.get("durability", 1.0)
	instance.unique_id = data.get("unique_id", instance.unique_id)
	instance.modifications = data.get("modifications", {})
	instance.instance_properties = data.get("instance_properties", {})
	instance.created_timestamp = data.get("created_timestamp", Time.get_unix_time_from_system())
	instance.modified_timestamp = data.get("modified_timestamp", Time.get_unix_time_from_system())
	
	return instance
